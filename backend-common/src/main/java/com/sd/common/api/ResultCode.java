package com.sd.common.api;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 结果码枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ResultCode implements IResultCode {

    /**
     * 操作成功
     */
    SUCCESS(200, "操作成功"),

    /**
     * 操作失败
     */
    FAILURE(400, "操作失败"),

    /**
     * 未认证
     */
    UNAUTHORIZED(401, "未认证"),

    /**
     * 未授权
     */
    FORBIDDEN(403, "未授权"),

    /**
     * 资源不存在
     */
    NOT_FOUND(404, "资源不存在"),

    /**
     * 服务器内部错误
     */
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),

    /**
     * 参数错误
     */
    PARAM_ERROR(1000, "参数错误"),

    /**
     * 用户名或密码错误
     */
    USERNAME_OR_PASSWORD_ERROR(1001, "用户名或密码错误"),

    /**
     * 用户不存在
     */
    USER_NOT_FOUND(1002, "用户不存在"),

    /**
     * 用户已禁用
     */
    USER_DISABLED(1003, "用户已禁用"),

    /**
     * 验证码错误
     */
    CAPTCHA_ERROR(1004, "验证码错误"),

    /**
     * 多因素认证失败
     */
    MFA_ERROR(1005, "多因素认证失败"),

    /**
     * 令牌过期
     */
    TOKEN_EXPIRED(1006, "令牌过期"),

    /**
     * 令牌无效
     */
    TOKEN_INVALID(1007, "令牌无效"),

    /**
     * 刷新令牌过期
     */
    REFRESH_TOKEN_EXPIRED(1008, "刷新令牌过期"),

    /**
     * 刷新令牌无效
     */
    REFRESH_TOKEN_INVALID(1009, "刷新令牌无效"),

    /**
     * 项目不存在
     */
    PROJECT_NOT_FOUND(2000, "项目不存在"),

    /**
     * 项目已禁用
     */
    PROJECT_DISABLED(2001, "项目已禁用"),

    /**
     * 项目成员不存在
     */
    PROJECT_MEMBER_NOT_FOUND(2002, "项目成员不存在"),

    /**
     * 项目成员已禁用
     */
    PROJECT_MEMBER_DISABLED(2003, "项目成员已禁用"),

    /**
     * Cloudflare账号不存在
     */
    CLOUDFLARE_ACCOUNT_NOT_FOUND(3000, "Cloudflare账号不存在"),

    /**
     * Cloudflare账号已禁用
     */
    CLOUDFLARE_ACCOUNT_DISABLED(3001, "Cloudflare账号已禁用"),

    /**
     * Cloudflare账号验证失败
     */
    CLOUDFLARE_ACCOUNT_VERIFY_FAILED(3002, "Cloudflare账号验证失败"),

    /**
     * 项目-账号映射不存在
     */
    PROJECT_ACCOUNT_MAPPING_NOT_FOUND(3003, "项目-账号映射不存在"),

    /**
     * 项目-账号映射已禁用
     */
    PROJECT_ACCOUNT_MAPPING_DISABLED(3004, "项目-账号映射已禁用"),

    /**
     * 域名不存在
     */
    DOMAIN_NOT_FOUND(4000, "域名不存在"),

    /**
     * 域名已禁用
     */
    DOMAIN_DISABLED(4001, "域名已禁用"),

    /**
     * 域名分组不存在
     */
    DOMAIN_GROUP_NOT_FOUND(4002, "域名分组不存在"),

    /**
     * 域名分组已禁用
     */
    DOMAIN_GROUP_DISABLED(4003, "域名分组已禁用"),

    /**
     * DNS记录不存在
     */
    DNS_RECORD_NOT_FOUND(5000, "DNS记录不存在"),

    /**
     * DNS记录已禁用
     */
    DNS_RECORD_DISABLED(5001, "DNS记录已禁用"),

    /**
     * IP池不存在
     */
    IP_POOL_NOT_FOUND(6000, "IP池不存在"),

    /**
     * IP池已禁用
     */
    IP_POOL_DISABLED(6001, "IP池已禁用"),

    /**
     * IP地址不存在
     */
    IP_ADDRESS_NOT_FOUND(6002, "IP地址不存在"),

    /**
     * IP地址已禁用
     */
    IP_ADDRESS_DISABLED(6003, "IP地址已禁用"),

    /**
     * 域名-IP映射不存在
     */
    DOMAIN_IP_MAPPING_NOT_FOUND(6004, "域名-IP映射不存在"),

    /**
     * 域名-IP映射已禁用
     */
    DOMAIN_IP_MAPPING_DISABLED(6005, "域名-IP映射已禁用"),

    /**
     * 证书不存在
     */
    CERTIFICATE_NOT_FOUND(7000, "证书不存在"),

    /**
     * 证书已禁用
     */
    CERTIFICATE_DISABLED(7001, "证书已禁用"),

    /**
     * 证书申请失败
     */
    CERTIFICATE_APPLY_FAILED(7002, "证书申请失败"),

    /**
     * 证书续期失败
     */
    CERTIFICATE_RENEW_FAILED(7003, "证书续期失败"),

    /**
     * Cloudflare API调用失败
     */
    CLOUDFLARE_API_CALL_FAILED(8000, "Cloudflare API调用失败"),

    /**
     * Cloudflare API配额超限
     */
    CLOUDFLARE_API_QUOTA_EXCEEDED(8001, "Cloudflare API配额超限"),

    /**
     * 批量操作部分失败
     */
    BATCH_OPERATION_PARTIALLY_FAILED(9000, "批量操作部分失败"),

    /**
     * 批量操作全部失败
     */
    BATCH_OPERATION_ALL_FAILED(9001, "批量操作全部失败"),

    /**
     * DNS模板不存在
     */
    TEMPLATE_NOT_FOUND(10000, "DNS模板不存在"),

    /**
     * DNS模板已禁用
     */
    TEMPLATE_DISABLED(10001, "DNS模板已禁用"),

    /**
     * DNS服务提供商账号不存在
     */
    DNS_PROVIDER_ACCOUNT_NOT_FOUND(11000, "DNS服务提供商账号不存在"),

    /**
     * DNS服务提供商账号验证失败
     */
    DNS_PROVIDER_ACCOUNT_VERIFY_FAILED(11001, "DNS服务提供商账号验证失败"),

    /**
     * DNS模板项不存在
     */
    TEMPLATE_ITEM_NOT_FOUND(10002, "DNS模板项不存在");

    /**
     * 状态码
     */
    private final int code;

    /**
     * 消息
     */
    private final String message;
}
