package com.sd.common.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 分页结果类
 *
 * <AUTHOR>
 */
@Data
public class PageResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据列表
     */
    private List<T> list;

    /**
     * 总数
     */
    private long total;

    /**
     * 当前页码
     */
    private int current;

    /**
     * 每页大小
     */
    private int pageSize;

    /**
     * 构造方法
     */
    public PageResult() {
        this.list = Collections.emptyList();
        this.total = 0;
        this.current = 1;
        this.pageSize = 10;
    }

    /**
     * 构造方法
     *
     * @param list     数据列表
     * @param total    总数
     * @param current  当前页码
     * @param pageSize 每页大小
     */
    public PageResult(List<T> list, long total, int current, int pageSize) {
        this.list = list;
        this.total = total;
        this.current = current;
        this.pageSize = pageSize;
    }

    /**
     * 构造方法
     *
     * @param list        数据列表
     * @param total       总数
     * @param pageRequest 分页请求
     */
    public PageResult(List<T> list, long total, PageRequest pageRequest) {
        this.list = list;
        this.total = total;
        this.current = pageRequest.getCurrent();
        this.pageSize = pageRequest.getPageSize();
    }

    public PageResult(Page<T> page) {
        this(page.getRecords(), page.getTotal(), (int) page.getCurrent(), (int) page.getSize());
    }

    /**
     * 空结果
     *
     * @param <T> 数据类型
     * @return 分页结果
     */
    public static <T> PageResult<T> empty() {
        return new PageResult<>();
    }

    /**
     * 空结果
     *
     * @param pageRequest 分页请求
     * @param <T>         数据类型
     * @return 分页结果
     */
    public static <T> PageResult<T> empty(PageRequest pageRequest) {
        return new PageResult<>(Collections.emptyList(), 0, pageRequest);
    }
}
