package com.sd.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 修改支付密码DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Schema(description = "修改支付密码参数")
public class ChangePayPasswordDTO {

    @NotBlank(message = "旧支付密码不能为空")
    @Schema(description = "旧支付密码", required = true)
    private String oldPayPassword;

    @NotBlank(message = "新支付密码不能为空")
    @Size(min = 6, max = 20, message = "新支付密码长度必须在6-20个字符之间")
    @Schema(description = "新支付密码", required = true)
    private String newPayPassword;
}
