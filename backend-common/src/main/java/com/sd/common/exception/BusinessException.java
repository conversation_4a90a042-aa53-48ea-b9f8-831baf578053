package com.sd.common.exception;

import com.sd.common.api.IResultCode;
import com.sd.common.api.ResultCode;
import lombok.Getter;

/**
 * 业务异常类
 *
 * <AUTHOR>
 */
@Getter
public class BusinessException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private final int code;

    /**
     * 构造方法
     *
     * @param message 消息
     */
    public BusinessException(String message) {
        super(message);
        this.code = ResultCode.FAILURE.getCode();
    }

    /**
     * 构造方法
     *
     * @param code    状态码
     * @param message 消息
     */
    public BusinessException(int code, String message) {
        super(message);
        this.code = code;
    }

    /**
     * 构造方法
     *
     * @param resultCode 结果码
     */
    public BusinessException(IResultCode resultCode) {
        super(resultCode.getMessage());
        this.code = resultCode.getCode();
    }

    /**
     * 构造方法
     *
     * @param resultCode 结果码
     * @param cause      原因
     */
    public BusinessException(IResultCode resultCode, Throwable cause) {
        super(resultCode.getMessage(), cause);
        this.code = resultCode.getCode();
    }

    /**
     * 构造方法
     *
     * @param message 消息
     * @param cause   原因
     */
    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.code = ResultCode.FAILURE.getCode();
    }
}
