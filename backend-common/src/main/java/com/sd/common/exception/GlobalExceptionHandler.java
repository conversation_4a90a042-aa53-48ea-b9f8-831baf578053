package com.sd.common.exception;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import com.sd.common.api.R;
import com.sd.common.api.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.List;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     *
     * @param e 业务异常
     * @return 响应对象
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.OK)
    public R<Object> handleBusinessException(BusinessException e) {
        log.error("业务异常：{}", e.getMessage(), e);
        return R.fail(e.getCode(), e.getMessage());
    }

    /**
     * 处理未登录异常
     *
     * @param e 未登录异常
     * @return 响应对象
     */
    @ExceptionHandler(NotLoginException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public R<Object> handleNotLoginException(NotLoginException e) {
        log.error("未登录异常：{}", e.getMessage(), e);
        return R.fail(ResultCode.UNAUTHORIZED.getCode(), "用户未登录");
    }

    /**
     * 处理无权限异常
     *
     * @param e 无权限异常
     * @return 响应对象
     */
    @ExceptionHandler(NotPermissionException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public R<Object> handleNotPermissionException(NotPermissionException e) {
        log.error("无权限异常：{}", e.getMessage(), e);
        return R.fail(ResultCode.FORBIDDEN.getCode(), "无权限访问");
    }

    /**
     * 处理无角色异常
     *
     * @param e 无角色异常
     * @return 响应对象
     */
    @ExceptionHandler(NotRoleException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public R<Object> handleNotRoleException(NotRoleException e) {
        log.error("无角色异常：{}", e.getMessage(), e);
        return R.fail(ResultCode.FORBIDDEN.getCode(), "无角色访问");
    }

    /**
     * 处理参数校验异常
     *
     * @param e 参数校验异常
     * @return 响应对象
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Object> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        log.error("参数校验异常：{}", e.getMessage(), e);
        BindingResult bindingResult = e.getBindingResult();
        List<FieldError> fieldErrors = bindingResult.getFieldErrors();
        StringBuilder sb = new StringBuilder();
        for (FieldError fieldError : fieldErrors) {
            sb.append(fieldError.getField()).append(": ").append(fieldError.getDefaultMessage()).append(", ");
        }
        String message = sb.toString();
        if (message.endsWith(", ")) {
            message = message.substring(0, message.length() - 2);
        }
        return R.fail(ResultCode.PARAM_ERROR.getCode(), message);
    }

    /**
     * 处理参数绑定异常
     *
     * @param e 参数绑定异常
     * @return 响应对象
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Object> handleBindException(BindException e) {
        log.error("参数绑定异常：{}", e.getMessage(), e);
        BindingResult bindingResult = e.getBindingResult();
        List<FieldError> fieldErrors = bindingResult.getFieldErrors();
        StringBuilder sb = new StringBuilder();
        for (FieldError fieldError : fieldErrors) {
            sb.append(fieldError.getField()).append(": ").append(fieldError.getDefaultMessage()).append(", ");
        }
        String message = sb.toString();
        if (message.endsWith(", ")) {
            message = message.substring(0, message.length() - 2);
        }
        return R.fail(ResultCode.PARAM_ERROR.getCode(), message);
    }

    /**
     * 处理其他异常
     *
     * @param e 其他异常
     * @return 响应对象
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R<Object> handleException(Exception e) {
        log.error("系统异常：{}", e.getMessage(), e);
        return R.fail(ResultCode.INTERNAL_SERVER_ERROR);
    }
}
