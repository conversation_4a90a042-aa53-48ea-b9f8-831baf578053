package com.sd.common.util;

import java.security.SecureRandom;

/**
 * 邀请码生成工具类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public class InviteCodeUtil {

    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final int CODE_LENGTH = 8;
    private static final SecureRandom RANDOM = new SecureRandom();

    /**
     * 生成随机邀请码
     *
     * @return 邀请码
     */
    public static String generateInviteCode() {
        StringBuilder sb = new StringBuilder(CODE_LENGTH);
        for (int i = 0; i < CODE_LENGTH; i++) {
            int index = RANDOM.nextInt(CHARACTERS.length());
            sb.append(CHARACTERS.charAt(index));
        }
        return sb.toString();
    }

    /**
     * 生成唯一邀请码（带时间戳）
     *
     * @return 邀请码
     */
    public static String generateUniqueInviteCode() {
        String timestamp = String.valueOf(System.currentTimeMillis() % 10000);
        String randomPart = generateInviteCode().substring(0, 4);
        return randomPart + timestamp;
    }
}
