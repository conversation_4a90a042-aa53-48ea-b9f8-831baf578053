package com.sd.common.common;

import lombok.Data;

import java.io.Serializable;

/**
 * 通用返回结果
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class Result<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 成功
     */
    public static final int SUCCESS = 200;

    /**
     * 失败
     */
    public static final int ERROR = 500;

    /**
     * 状态码
     */
    private int code;

    /**
     * 返回内容
     */
    private String message;

    /**
     * 数据对象
     */
    private T data;

    /**
     * 初始化一个新创建的 Result 对象，使其表示一个空消息。
     */
    public Result() {
    }

    /**
     * 初始化一个新创建的 Result 对象
     *
     * @param code 状态码
     * @param message 返回内容
     */
    public Result(int code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 初始化一个新创建的 Result 对象
     *
     * @param code 状态码
     * @param message 返回内容
     * @param data 数据对象
     */
    public Result(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    /**
     * 返回成功消息
     *
     * @return 成功消息
     */
    public static <T> Result<T> success() {
        return Result.success("操作成功");
    }

    /**
     * 返回成功数据
     *
     * @return 成功消息
     */
    public static <T> Result<T> success(T data) {
        return Result.success("操作成功", data);
    }

    /**
     * 返回成功消息
     *
     * @param message 返回内容
     * @return 成功消息
     */
    public static <T> Result<T> success(String message) {
        return Result.success(message, null);
    }

    /**
     * 返回成功消息
     *
     * @param message 返回内容
     * @param data 数据对象
     * @return 成功消息
     */
    public static <T> Result<T> success(String message, T data) {
        return new Result<>(SUCCESS, message, data);
    }

    /**
     * 返回错误消息
     *
     * @return 错误消息
     */
    public static <T> Result<T> error() {
        return Result.error("操作失败");
    }

    /**
     * 返回错误消息
     *
     * @param message 返回内容
     * @return 错误消息
     */
    public static <T> Result<T> error(String message) {
        return Result.error(message, null);
    }

    /**
     * 返回错误消息
     *
     * @param message 返回内容
     * @param data 数据对象
     * @return 错误消息
     */
    public static <T> Result<T> error(String message, T data) {
        return new Result<>(ERROR, message, data);
    }

    /**
     * 返回错误消息
     *
     * @param code 状态码
     * @param message 返回内容
     * @return 错误消息
     */
    public static <T> Result<T> error(int code, String message) {
        return new Result<>(code, message, null);
    }

    /**
     * 方便链式调用
     *
     * @param message 返回内容
     * @return 本身
     */
    public Result<T> message(String message) {
        this.setMessage(message);
        return this;
    }

    /**
     * 方便链式调用
     *
     * @param code 状态码
     * @return 本身
     */
    public Result<T> code(int code) {
        this.setCode(code);
        return this;
    }
}
