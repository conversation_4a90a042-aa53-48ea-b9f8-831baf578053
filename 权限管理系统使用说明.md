# 权限管理系统使用说明

## 概述

本权限管理系统基于 **Spring Boot 3 + MyBatis Plus + Sa-Token** 后端和 **UmiJS + Ant Design** 前端构建，提供了完整的RBAC（基于角色的访问控制）权限管理功能。

## 功能特性

### 后端功能
- ✅ **用户管理** - 创建、编辑、删除用户，支持密码重置
- ✅ **角色管理** - 创建、编辑、删除角色，支持数据权限控制
- ✅ **权限管理** - 树形权限结构，支持目录、菜单、按钮三种类型
- ✅ **用户角色分配** - 为用户分配多个角色
- ✅ **角色权限分配** - 为角色分配权限
- ✅ **权限校验** - 基于Sa-Token的注解式权限控制
- ✅ **缓存支持** - Redis缓存提升性能
- ✅ **数据权限** - 支持全部、部门、个人等数据范围控制

### 前端功能
- ✅ **用户管理页面** - 用户的增删改查，角色分配，密码重置
- ✅ **角色管理页面** - 角色的增删改查，权限分配
- ✅ **权限管理页面** - 权限树的管理
- ✅ **用户角色分配** - 可视化的用户角色分配界面
- ✅ **权限树组件** - 支持树形结构的权限选择
- ✅ **响应式设计** - 适配不同屏幕尺寸

## 数据库设计

### 核心表结构

1. **sys_user** - 用户表
   - 存储用户基本信息
   - 支持用户名、邮箱、手机号唯一性约束

2. **sys_role** - 角色表
   - 存储角色基本信息
   - 支持数据权限范围配置

3. **sys_permission** - 权限表
   - 树形结构存储权限
   - 支持目录、菜单、按钮三种类型

4. **sys_user_role** - 用户角色关联表
   - 多对多关系
   - 支持一个用户拥有多个角色

5. **sys_role_permission** - 角色权限关联表
   - 多对多关系
   - 支持一个角色拥有多个权限

## 快速开始

### 1. 数据库初始化

执行 `backend-admin/src/main/resources/sql/permission_system.sql` 文件初始化数据库表和基础数据。

### 2. 后端启动

```bash
cd backend-admin
mvn spring-boot:run
```

后端服务将在 http://localhost:8080 启动。

### 3. 前端启动

```bash
cd frontend-admin
pnpm install
pnpm dev
```

前端服务将在 http://localhost:8000 启动。

## API 接口说明

### 用户管理接口

| 方法 | 路径 | 说明 | 权限 |
|------|------|------|------|
| GET | /api/sys-user/page | 分页查询用户列表 | system:user:view |
| GET | /api/sys-user/{id} | 根据ID查询用户详情 | system:user:view |
| POST | /api/sys-user | 创建用户 | system:user:add |
| PUT | /api/sys-user | 更新用户 | system:user:edit |
| DELETE | /api/sys-user/{id} | 删除用户 | system:user:delete |
| DELETE | /api/sys-user/batch | 批量删除用户 | system:user:delete |
| PUT | /api/sys-user/reset-password | 重置用户密码 | system:user:resetPassword |

### 角色管理接口

| 方法 | 路径 | 说明 | 权限 |
|------|------|------|------|
| GET | /api/role/page | 分页查询角色列表 | system:role:view |
| GET | /api/role/list | 查询所有角色列表 | system:role:view |
| GET | /api/role/{id} | 根据ID查询角色详情 | system:role:view |
| POST | /api/role | 创建角色 | system:role:add |
| PUT | /api/role | 更新角色 | system:role:edit |
| DELETE | /api/role/{id} | 删除角色 | system:role:delete |
| DELETE | /api/role/batch | 批量删除角色 | system:role:delete |

### 权限管理接口

| 方法 | 路径 | 说明 | 权限 |
|------|------|------|------|
| GET | /api/permission/tree | 查询权限树 | system:permission:view |
| GET | /api/permission/{id} | 根据ID查询权限详情 | system:permission:view |
| POST | /api/permission | 创建权限 | system:permission:add |
| PUT | /api/permission | 更新权限 | system:permission:edit |
| DELETE | /api/permission/{id} | 删除权限 | system:permission:delete |

### 用户角色管理接口

| 方法 | 路径 | 说明 | 权限 |
|------|------|------|------|
| POST | /api/user-role/assign | 分配用户角色 | system:user:edit |
| GET | /api/user-role/user/{userId} | 根据用户ID查询角色ID列表 | system:user:view |
| GET | /api/user-role/role/{roleId} | 根据角色ID查询用户ID列表 | system:role:view |

## 权限控制使用

### 后端权限控制

使用Sa-Token注解进行权限控制：

```java
@RestController
@RequestMapping("/role")
public class RoleController {

    @GetMapping("/page")
    @SaCheckPermission(PermissionConstants.ROLE_VIEW)
    public R<PageResult<RoleVO>> page(@Valid RoleQueryDTO queryDTO) {
        // 业务逻辑
    }

    @PostMapping
    @SaCheckPermission(PermissionConstants.ROLE_ADD)
    public R<Long> create(@Valid @RequestBody RoleCreateDTO createDTO) {
        // 业务逻辑
    }
}
```

### 前端权限控制

前端可以通过用户权限信息控制页面元素的显示：

```tsx
// 根据权限显示按钮
{hasPermission('system:role:add') && (
  <Button type="primary" onClick={handleAdd}>
    新建角色
  </Button>
)}
```

## 默认数据

系统初始化时会创建以下默认数据：

### 默认用户
- **admin** - 超级管理员，密码：123456
- **manager** - 管理员，密码：123456
- **user** - 普通用户，密码：123456

### 默认角色
- **超级管理员** (super_admin) - 拥有所有权限
- **管理员** (admin) - 拥有大部分权限
- **普通用户** (user) - 拥有基本查询权限

### 默认权限
- **系统管理** - 系统管理目录
  - **用户管理** - 用户管理菜单及相关按钮权限
  - **角色管理** - 角色管理菜单及相关按钮权限
  - **权限管理** - 权限管理菜单及相关按钮权限

## 扩展开发

### 添加新的权限

1. 在 `PermissionConstants` 类中定义权限常量
2. 在数据库中插入权限数据
3. 在Controller方法上添加 `@SaCheckPermission` 注解
4. 在前端页面中根据权限控制元素显示

### 添加新的角色

1. 在数据库中插入角色数据
2. 为角色分配相应的权限
3. 在业务逻辑中使用角色进行权限判断

## 注意事项

1. **权限缓存** - 系统使用Redis缓存权限信息，修改权限后需要清除缓存
2. **数据权限** - 目前支持5种数据权限范围，可根据业务需求扩展
3. **权限继承** - 子权限会继承父权限的访问控制
4. **安全性** - 所有敏感操作都需要权限验证，前端验证只是用户体验优化

## 技术栈

### 后端
- Spring Boot 3.2.0
- MyBatis Plus 3.5.5
- Sa-Token 1.37.0
- MySQL 8.0
- Redis
- Swagger 3

### 前端
- UmiJS 4
- React 18
- Ant Design 5
- TypeScript
- ProComponents

## 联系方式

如有问题或建议，请联系开发团队。
