# 全栈项目

本项目包含一个后端应用和两个前端应用：

## 🚀 项目概览

- **后端项目**: Spring Boot 3 + MyBatis Plus + Sa-Token
- **用户端前端**: React + TypeScript + Ant Design Mobile
- **管理后台前端**: UmiJS + Ant Design + TypeScript

## 📱 用户端前端项目 (frontend-user)

基于 **React + TypeScript + Ant Design Mobile** 的用户端前端项目。

### 技术栈
- React 19
- TypeScript
- Ant Design Mobile
- Create React App

### 启动项目
```bash
cd frontend-user
npm start
```

### 项目特点
- 移动端优先设计
- 使用 Ant Design Mobile 组件库
- 适合用户端应用开发

## 🖥️ 管理后台前端项目 (frontend-admin)

基于 **UmiJS + Ant Design + TypeScript** 的管理后台前端项目。

### 技术栈
- UmiJS 4
- React
- TypeScript
- Ant Design
- pnpm

### 启动项目
```bash
cd frontend-admin
pnpm dev
```

### 项目特点
- 企业级后台管理系统
- 使用 UmiJS 框架
- 集成 Ant Design 组件库
- 适合管理后台开发

## 📁 项目结构

```
.
├── src/                    # 后端源码目录
│   ├── main/
│   │   ├── java/
│   │   └── resources/
│   └── test/
├── frontend-user/          # 用户端前端项目
│   ├── src/
│   ├── public/
│   ├── package.json
│   └── ...
├── frontend-admin/         # 管理后台前端项目
│   ├── src/
│   ├── package.json
│   └── ...
├── pom.xml                 # Maven 配置文件
├── backend-README.md       # 后端项目详细说明
└── README.md               # 项目总体说明
```

## 🚀 快速开始

### 后端项目
```bash
# 编译项目
mvn clean compile

# 运行项目
mvn spring-boot:run
```

### 前端项目

1. **用户端项目**：
   ```bash
   cd frontend-user
   npm install
   npm start
   ```

2. **管理后台项目**：
   ```bash
   cd frontend-admin
   pnpm install
   pnpm dev
   ```

## 🔧 后端项目 (根目录)

基于 **Spring Boot 3 + MyBatis Plus + Sa-Token** 的后端项目。

### 技术栈
- Spring Boot 3.2.0
- MyBatis Plus 3.5.5
- Sa-Token 1.37.0
- MySQL 8.0
- Redis
- Druid 连接池

### 启动项目
```bash
# 编译项目
mvn clean compile

# 运行项目
mvn spring-boot:run
```

### 项目特点
- 基于 Sa-Token 的权限认证
- MyBatis Plus 分页和代码生成
- 全局异常处理
- 自动填充创建/更新时间
- BCrypt 密码加密

## 📝 说明

- **后端项目**：位于根目录，使用 Maven 管理依赖
- **用户端项目**：使用 npm 作为包管理器
- **管理后台项目**：使用 pnpm 作为包管理器
- 三个项目可以独立开发和部署
- 根据项目需求选择合适的技术栈
