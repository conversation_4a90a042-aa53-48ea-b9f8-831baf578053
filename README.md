# 管理后台前端项目

基于 UmiJS 4 + Ant Design 5 + TypeScript 的现代化管理后台前端项目。

## 🚀 技术栈

### 核心框架
- **React 18.3.1** - 最新的React框架
- **UmiJS 4.4.11** - 企业级前端应用框架
- **TypeScript 5.8.3** - 类型安全的JavaScript超集

### UI组件库
- **Ant Design 5.25.2** - 企业级UI设计语言
- **@ant-design/pro-components 2.8.7** - 高级组件库
- **@ant-design/icons 6.0.0** - 图标库

### 开发工具
- **ESLint 9.18.0** - 代码质量检查
- **Prettier 3.4.2** - 代码格式化
- **pnpm** - 高效的包管理器

## 📦 依赖版本

### 生产依赖
```json
{
  "@ant-design/icons": "^6.0.0",
  "@ant-design/pro-components": "^2.8.7",
  "@ant-design/pro-layout": "^7.22.4",
  "@ant-design/pro-table": "^3.19.0",
  "@ant-design/pro-form": "^2.31.7",
  "@ant-design/pro-field": "^3.0.4",
  "@ant-design/pro-utils": "^2.17.0",
  "@umijs/max": "^4.4.11",
  "antd": "^5.25.2",
  "dayjs": "^1.11.13",
  "lodash-es": "^4.17.21",
  "react": "^18.3.1",
  "react-dom": "^18.3.1",
  "umi": "^4.4.11"
}
```

### 开发依赖
```json
{
  "@types/lodash-es": "^4.17.12",
  "@types/node": "^22.15.21",
  "@types/react": "^19.1.5",
  "@types/react-dom": "^19.1.5",
  "@typescript-eslint/eslint-plugin": "^8.20.0",
  "@typescript-eslint/parser": "^8.20.0",
  "eslint": "^9.18.0",
  "eslint-plugin-react": "^7.37.2",
  "eslint-plugin-react-hooks": "^5.1.0",
  "prettier": "^3.4.2",
  "typescript": "^5.8.3"
}
```

## 🛠️ 开发命令

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build

# 代码检查
pnpm lint

# 类型检查
pnpm type-check

# 清理缓存
pnpm clean

# 更新依赖到最新版本
./update-deps.sh
```

## 🔧 配置说明

### UmiJS 配置
- 支持最新的 UmiJS 4 特性
- 集成 Ant Design 5 主题配置
- 配置代理转发到后端API
- 支持国际化和多语言

### TypeScript 配置
- 启用严格模式
- 支持最新的ES2022特性
- 配置路径别名 `@/*` 指向 `src/*`

### ESLint 配置
- 集成 TypeScript 支持
- React Hooks 规则检查
- 自动修复代码格式问题

### Prettier 配置
- 统一的代码格式化规则
- 支持 TypeScript 和 JSX

## 📁 项目结构

```
frontend-admin/
├── src/
│   ├── components/          # 公共组件
│   ├── pages/              # 页面组件
│   │   ├── System/         # 系统管理页面
│   │   │   ├── User/       # 用户管理
│   │   │   ├── Role/       # 角色管理
│   │   │   └── Permission/ # 权限管理
│   │   ├── index.tsx       # 首页
│   │   └── docs.tsx        # 文档页面
│   ├── services/           # API服务
│   ├── types/              # 类型定义
│   └── utils/              # 工具函数
├── .eslintrc.js            # ESLint配置
├── .prettierrc.js          # Prettier配置
├── .umirc.ts               # UmiJS配置
├── tsconfig.json           # TypeScript配置
├── package.json            # 项目配置
└── update-deps.sh          # 依赖更新脚本
```

## 🌟 特性

- ✅ **最新技术栈** - 使用最新版本的React、UmiJS、Ant Design
- ✅ **TypeScript** - 完整的类型支持和类型安全
- ✅ **代码质量** - ESLint + Prettier 保证代码质量
- ✅ **开发体验** - 热重载、快速构建、智能提示
- ✅ **组件库** - 丰富的Ant Design Pro组件
- ✅ **国际化** - 支持多语言切换
- ✅ **主题定制** - 支持Ant Design主题定制
- ✅ **代理配置** - 开发环境API代理

## 🔄 更新依赖

项目提供了自动更新脚本，可以一键更新所有依赖到最新版本：

```bash
# 运行更新脚本
./update-deps.sh
```

该脚本会：
1. 停止开发服务器
2. 清理缓存和依赖
3. 安装最新依赖
4. 检查关键依赖版本
5. 运行类型检查

## 📝 开发规范

### 组件开发
- 使用函数式组件和Hooks
- 组件必须有TypeScript类型定义
- 使用Ant Design组件库
- 遵循React最佳实践

### 代码规范
- 使用ESLint进行代码检查
- 使用Prettier进行代码格式化
- 提交前自动运行代码检查
- 遵循TypeScript严格模式

### API调用
- 使用统一的API类型定义
- 所有接口调用必须有错误处理
- 使用UmiJS的request插件

## 🚀 部署

```bash
# 构建生产版本
pnpm build

# 构建产物在 dist 目录
```

## 📞 支持

如有问题，请联系开发团队或查看项目文档。
