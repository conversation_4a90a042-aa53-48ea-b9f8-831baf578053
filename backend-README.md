# Backend Java 项目

基于 **Spring Boot 3 + MyBatis Plus + Sa-Token** 的后端项目。

## 🚀 技术栈

- **Spring Boot 3.2.0** - 主框架
- **MyBatis Plus 3.5.5** - ORM框架
- **Sa-Token 1.37.0** - 权限认证框架
- **MySQL 8.0** - 数据库
- **Druid** - 数据库连接池
- **Redis** - 缓存
- **Hutool** - 工具类库
- **Lombok** - 简化代码

## 📁 项目结构

```
backend-java/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/example/backend/
│   │   │       ├── BackendApplication.java          # 启动类
│   │   │       ├── common/                          # 通用类
│   │   │       │   └── Result.java                  # 统一返回结果
│   │   │       ├── config/                          # 配置类
│   │   │       │   ├── SaTokenConfig.java           # Sa-Token配置
│   │   │       │   ├── MyBatisPlusConfig.java       # MyBatis Plus配置
│   │   │       │   ├── MetaObjectHandler.java       # 自动填充处理器
│   │   │       │   └── GlobalExceptionHandler.java  # 全局异常处理
│   │   │       ├── controller/                      # 控制器
│   │   │       │   ├── AuthController.java          # 认证控制器
│   │   │       │   └── UserController.java          # 用户控制器
│   │   │       ├── entity/                          # 实体类
│   │   │       │   ├── BaseEntity.java              # 基础实体
│   │   │       │   └── User.java                    # 用户实体
│   │   │       ├── mapper/                          # Mapper接口
│   │   │       │   └── UserMapper.java              # 用户Mapper
│   │   │       └── service/                         # 服务层
│   │   │           ├── UserService.java             # 用户服务接口
│   │   │           └── impl/
│   │   │               └── UserServiceImpl.java     # 用户服务实现
│   │   └── resources/
│   │       ├── application.yml                      # 配置文件
│   │       └── sql/
│   │           └── init.sql                         # 数据库初始化脚本
│   └── test/
└── pom.xml                                          # Maven配置
```

## 🛠️ 快速开始

### 1. 环境要求

- JDK 17+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

### 2. 数据库配置

1. 创建数据库：
```sql
CREATE DATABASE backend_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 执行初始化脚本：
```bash
mysql -u root -p backend_db < src/main/resources/sql/init.sql
```

### 3. 修改配置

编辑 `src/main/resources/application.yml`，修改数据库和Redis连接信息：

```yaml
spring:
  datasource:
    url: ***************************************************************************************************************************************************
    username: root
    password: 你的密码
  data:
    redis:
      host: localhost
      port: 6379
      password: 你的Redis密码
```

### 4. 启动项目

```bash
# 进入项目目录
cd backend-java

# 安装依赖
mvn clean install

# 启动项目
mvn spring-boot:run
```

项目启动后访问：http://localhost:8080/api

## 📋 API 接口

### 认证相关

| 方法 | 路径 | 说明 | 是否需要登录 |
|------|------|------|-------------|
| POST | /api/auth/login | 用户登录 | ❌ |
| POST | /api/auth/register | 用户注册 | ❌ |
| POST | /api/auth/logout | 用户登出 | ✅ |
| GET | /api/auth/userinfo | 获取当前用户信息 | ✅ |
| GET | /api/auth/isLogin | 检查登录状态 | ❌ |

### 用户管理

| 方法 | 路径 | 说明 | 是否需要登录 |
|------|------|------|-------------|
| GET | /api/user/page | 分页查询用户 | ✅ |
| GET | /api/user/{id} | 根据ID查询用户 | ✅ |
| POST | /api/user | 新增用户 | ✅ |
| PUT | /api/user | 更新用户 | ✅ |
| DELETE | /api/user/{id} | 删除用户 | ✅ |

### 测试账号

| 用户名 | 密码 | 角色 |
|--------|------|------|
| admin | 123456 | 管理员 |
| user | 123456 | 普通用户 |

## 🔧 功能特性

- ✅ **用户认证** - 基于Sa-Token的登录认证
- ✅ **权限控制** - 注解式权限控制
- ✅ **数据分页** - MyBatis Plus分页插件
- ✅ **自动填充** - 创建时间、更新时间自动填充
- ✅ **逻辑删除** - 软删除支持
- ✅ **全局异常处理** - 统一异常处理机制
- ✅ **数据库连接池** - Druid连接池监控
- ✅ **Redis缓存** - Sa-Token整合Redis
- ✅ **密码加密** - BCrypt密码加密

## 📝 开发说明

### 添加新的实体类

1. 继承 `BaseEntity` 获得基础字段
2. 使用 `@TableName` 指定表名
3. 使用 `@TableId` 指定主键策略

### 添加新的接口

1. 创建对应的 Mapper 接口继承 `BaseMapper`
2. 创建 Service 接口继承 `IService`
3. 创建 Service 实现类继承 `ServiceImpl`
4. 创建 Controller 并使用 `@SaCheckLogin` 注解

### 权限控制

使用Sa-Token注解进行权限控制：

```java
@SaCheckLogin                    // 登录校验
@SaCheckRole("admin")           // 角色校验
@SaCheckPermission("user:add")  // 权限校验
```

## 🐛 常见问题

1. **启动失败** - 检查数据库连接配置和Redis连接
2. **登录失败** - 确认用户名密码正确，检查数据库数据
3. **权限异常** - 确认已登录且具有相应权限

## 📄 许可证

MIT License
