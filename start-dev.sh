#!/bin/bash

# 用户端注册登录功能开发环境启动脚本

echo "🚀 启动用户端注册登录功能开发环境..."

# 检查是否安装了必要的工具
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 未安装，请先安装 $1"
        exit 1
    fi
}

echo "📋 检查环境依赖..."
check_command "java"
check_command "mvn"
check_command "node"
check_command "npm"
check_command "pnpm"

# 启动后端服务
echo "🔧 启动后端服务..."

# 启动用户端后端
echo "启动用户端后端 (端口 8081)..."
cd backend-user
mvn spring-boot:run &
USER_BACKEND_PID=$!
cd ..

# 等待一下让用户端后端启动
sleep 5

# 启动管理后台后端
echo "启动管理后台后端 (端口 8080)..."
cd backend-admin
mvn spring-boot:run &
ADMIN_BACKEND_PID=$!
cd ..

# 等待后端服务启动
echo "⏳ 等待后端服务启动..."
sleep 10

# 启动前端服务
echo "🎨 启动前端服务..."

# 启动用户端前端
echo "启动用户端前端 (端口 3000)..."
cd frontend-user
npm install
npm start &
USER_FRONTEND_PID=$!
cd ..

# 启动管理后台前端
echo "启动管理后台前端 (端口 8000)..."
cd frontend-admin
pnpm install
pnpm dev &
ADMIN_FRONTEND_PID=$!
cd ..

echo "✅ 所有服务启动完成！"
echo ""
echo "📱 用户端前端: http://localhost:3000"
echo "🖥️  管理后台前端: http://localhost:8000"
echo "🔧 用户端后端: http://localhost:8081"
echo "⚙️  管理后台后端: http://localhost:8080"
echo ""
echo "🔑 测试账号:"
echo "管理后台: admin / 123456"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
wait

# 清理进程
echo "🛑 停止所有服务..."
kill $USER_BACKEND_PID $ADMIN_BACKEND_PID $USER_FRONTEND_PID $ADMIN_FRONTEND_PID 2>/dev/null

echo "✅ 所有服务已停止"
