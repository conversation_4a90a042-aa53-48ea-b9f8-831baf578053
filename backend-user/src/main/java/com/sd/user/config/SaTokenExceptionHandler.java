package com.sd.user.config;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import com.sd.common.api.R;
import com.sd.common.api.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * Sa-Token异常处理器
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestControllerAdvice
public class SaTokenExceptionHandler {

    /**
     * 未登录异常处理
     */
    @ExceptionHandler(NotLoginException.class)
    public R<Void> handleNotLoginException(NotLoginException e) {
        log.warn("未登录异常：{}", e.getMessage());
        
        // 根据不同的未登录类型返回不同的消息
        String message = switch (e.getType()) {
            case NotLoginException.NOT_TOKEN -> "未提供登录凭证";
            case NotLoginException.INVALID_TOKEN -> "登录凭证无效";
            case NotLoginException.TOKEN_TIMEOUT -> "登录已过期，请重新登录";
            case NotLoginException.BE_REPLACED -> "您的账号已在其他地方登录";
            case NotLoginException.KICK_OUT -> "您已被强制下线";
            default -> "请先登录";
        };
        
        return R.fail(ResultCode.UNAUTHORIZED.getCode(), message);
    }

    /**
     * 权限不足异常处理
     */
    @ExceptionHandler(NotPermissionException.class)
    public R<Void> handleNotPermissionException(NotPermissionException e) {
        log.warn("权限不足异常：{}", e.getMessage());
        return R.fail(ResultCode.FORBIDDEN.getCode(), "权限不足：" + e.getPermission());
    }

    /**
     * 角色不足异常处理
     */
    @ExceptionHandler(NotRoleException.class)
    public R<Void> handleNotRoleException(NotRoleException e) {
        log.warn("角色不足异常：{}", e.getMessage());
        return R.fail(ResultCode.FORBIDDEN.getCode(), "角色不足：" + e.getRole());
    }
}
