-- 为app_user表添加支付密码和邀请码字段

-- 添加支付密码字段
ALTER TABLE `app_user` ADD COLUMN `pay_password` varchar(100) DEFAULT NULL COMMENT '支付密码' AFTER `password`;

-- 添加邀请码字段
ALTER TABLE `app_user` ADD COLUMN `invite_code` varchar(20) DEFAULT NULL COMMENT '邀请码' AFTER `last_login_ip`;

-- 为邀请码字段添加唯一索引
ALTER TABLE `app_user` ADD UNIQUE INDEX `uk_invite_code` (`invite_code`);

-- 为现有用户生成邀请码（可选，如果需要为现有用户生成邀请码）
-- UPDATE `app_user` SET `invite_code` = CONCAT('USR', LPAD(id, 6, '0')) WHERE `invite_code` IS NULL;
