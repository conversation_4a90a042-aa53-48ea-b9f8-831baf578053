-- 创建用户端数据库
CREATE DATABASE IF NOT EXISTS `user_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `user_db`;

-- 用户端用户表
DROP TABLE IF EXISTS `app_user`;
CREATE TABLE `app_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(30) DEFAULT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `nickname` varchar(30) NOT NULL COMMENT '昵称',
  `email` varchar(50) DEFAULT '' COMMENT '邮箱',
  `phone` varchar(11) DEFAULT '' COMMENT '手机号',
  `avatar` varchar(200) DEFAULT '' COMMENT '头像',
  `gender` tinyint DEFAULT '0' COMMENT '性别（0未知 1男 2女）',
  `birthday` varchar(20) DEFAULT '' COMMENT '生日',
  `bio` varchar(500) DEFAULT '' COMMENT '个人简介',
  `status` tinyint DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT '' COMMENT '最后登录IP',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_phone` (`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户端用户表';

-- 插入测试数据
INSERT INTO `app_user` VALUES 
(1, 'testuser', '$2a$10$7JB720yubVSOfvVMe6/b.eHZiNvVvb.OsFXjNWJx/2BMHb4H6P0S2', '测试用户', '<EMAIL>', '13888888888', '', 1, '1990-01-01', '这是一个测试用户', 0, NULL, '', NOW(), NOW(), NULL, NULL, 0),
(2, 'demouser', '$2a$10$7JB720yubVSOfvVMe6/b.eHZiNvVvb.OsFXjNWJx/2BMHb4H6P0S2', '演示用户', '<EMAIL>', '13999999999', '', 2, '1995-05-05', '这是一个演示用户', 0, NULL, '', NOW(), NOW(), NULL, NULL, 0);

-- 默认密码都是：123456
