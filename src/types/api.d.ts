declare namespace API {
  // 通用响应类型
  interface Response<T = any> {
    code: number;
    message: string;
    data: T;
    timestamp?: number;
  }

  // 分页结果类型
  interface PageResult<T = any> {
    list: T[];
    total: number;
    pageNum: number;
    pageSize: number;
  }

  // 分页请求类型
  interface PageRequest {
    current?: number;
    pageSize?: number;
    sorter?: string;
    searchCount?: boolean;
  }

  // 用户相关类型
  interface User {
    id: number;
    username: string;
    nickname: string;
    email?: string;
    phone?: string;
    avatar?: string;
    status: number;
    createTime: string;
    updateTime: string;
    roles?: Role[];
    roleNames?: string[];
  }

  // 角色相关类型
  interface Role {
    id: number;
    roleName: string;
    roleKey: string;
    description?: string;
    sort: number;
    status: number;
    dataScope: number;
    remark?: string;
    createTime: string;
    updateTime: string;
    permissions?: Permission[] | string[]; // 支持两种数据结构
  }

  // 权限相关类型
  interface Permission {
    id: number;
    permissionName: string;
    permissionKey: string;
    type: number;
    parentId: number;
    path?: string;
    component?: string;
    icon?: string;
    sort: number;
    isFrame: number;
    isCache: number;
    visible: number;
    status: number;
    remark?: string;
    createTime: string;
    updateTime: string;
    children?: Permission[];
  }

  // 登录相关类型
  interface LoginParams {
    username: string;
    password: string;
    captcha?: string;
    uuid?: string;
  }

  interface LoginResult {
    token: string;
    user: User;
    permissions?: string[];
    roles?: string[];
    tokenName?: string;
    tokenValue?: string;
    isLogin?: boolean;
    loginId?: any;
    loginType?: string;
    tokenTimeout?: number;
    sessionTimeout?: number;
    tokenSessionTimeout?: number;
    tokenActiveTimeout?: number;
  }

  // 菜单类型
  interface MenuItem {
    id: number;
    name: string;
    path: string;
    component?: string;
    icon?: string;
    parentId?: number;
    sort: number;
    type: number;
    visible: number;
    children?: MenuItem[];
  }

  // 当前用户类型
  interface CurrentUser {
    user?: User;
    username?: string;
    nickname?: string;
    avatar?: string;
    permissions?: string[];
    roles?: string[];
  }
}
