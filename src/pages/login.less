.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: auto;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.content {
  flex: 1;
  padding: 32px 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.top {
  text-align: center;
  margin-bottom: 40px;
}

.header {
  height: 44px;
  line-height: 44px;
  margin-bottom: 16px;
}

.title {
  font-size: 32px;
  color: #fff;
  font-weight: 600;
  position: relative;
}

.desc {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 12px;
}

.main {
  width: 368px;
  margin: 0 auto;
}

.loginCard {
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  
  :global(.ant-card-body) {
    padding: 40px;
  }
}

.forgot {
  float: right;
  color: #1890ff;
  
  &:hover {
    color: #40a9ff;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .main {
    width: 100%;
    max-width: 368px;
  }
  
  .loginCard {
    :global(.ant-card-body) {
      padding: 24px;
    }
  }
}
