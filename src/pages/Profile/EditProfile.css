.edit-profile-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.edit-profile-header {
  background: white;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.edit-profile-header h1 {
  flex: 1;
  text-align: center;
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.edit-profile-form {
  background: white;
  margin: 12px;
  border-radius: 12px;
  padding: 20px;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 16px;
  color: #666;
}

/* 表单项样式 */
.adm-form-item {
  margin-bottom: 20px;
}

.adm-form-item-label {
  font-weight: 500;
  color: #333;
}

/* 输入框样式 */
.adm-input {
  font-size: 16px;
  padding: 12px;
}

.adm-input:disabled {
  background-color: #f5f5f5;
  color: #999;
}

/* 单选按钮组样式 */
.adm-radio-group {
  display: flex;
  gap: 16px;
}

/* 文本域样式 */
.adm-text-area {
  font-size: 16px;
  padding: 12px;
}

/* 按钮样式 */
.adm-button {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}
