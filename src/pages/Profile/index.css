/* Profile页面样式 */
.profile-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.profile-content {
  padding: 16px;
  padding-bottom: 80px; /* 为底部导航留出空间 */
}

/* 加载和错误状态 */
.loading,
.error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #666;
}

/* 账户信息卡片 */
.profile-header {
  margin-bottom: 16px;
}

.account-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 用户基本信息 */
.account-header {
  display: flex;
  align-items: center;
  padding: 20px 16px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  overflow: hidden;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.avatar-icon {
  font-size: 28px;
  color: white;
}

.user-basic-info {
  flex: 1;
}

.username {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: white;
}

.extension-code {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.code-highlight {
  font-weight: 600;
  color: #ffd700;
}

/* 余额信息 */
.balance-section {
  padding: 20px 16px;
  background-color: white;
}

.total-balance {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.balance-label {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

.balance-amount {
  display: flex;
  align-items: center;
  gap: 12px;
}

.amount-value {
  font-size: 24px;
  font-weight: 700;
  color: #1890ff;
}

.add-money-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.add-money-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

.add-money-btn:active {
  transform: scale(0.95);
}

/* 余额明细 */
.balance-details {
  display: flex;
  gap: 16px;
}

.balance-item {
  flex: 1;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
}

.balance-item.available {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

.balance-item.frozen {
  background-color: #fff2e8;
  border: 1px solid #ffbb96;
}

.balance-item-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.balance-item-value {
  font-size: 16px;
  font-weight: 600;
}

.available-amount {
  color: #52c41a;
}

.frozen-amount {
  color: #fa8c16;
}

/* 功能菜单 */
.menu-section {
  margin-top: 16px;
}

.menu-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.function-menu {
  background-color: white;
}

.menu-item {
  padding: 16px !important;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f5f5f5;
}

.menu-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.menu-content {
  flex: 1;
}

.menu-title {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

/* 退出按钮特殊样式 */
.exit-item .menu-title {
  color: #ff4d4f;
}

.exit-item .menu-icon-wrapper {
  background-color: #fff2f0;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .profile-content {
    padding: 12px;
  }
  
  .balance-details {
    flex-direction: column;
    gap: 12px;
  }
  
  .username {
    font-size: 18px;
  }
  
  .amount-value {
    font-size: 20px;
  }
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .profile-page {
    background-color: #141414;
  }
  
  .account-card,
  .menu-card {
    background-color: #1f1f1f;
    border: 1px solid #303030;
  }
  
  .balance-section,
  .function-menu {
    background-color: #1f1f1f;
  }
  
  .menu-title {
    color: #fff;
  }
  
  .balance-label {
    color: #999;
  }
  
  .menu-item {
    border-bottom-color: #303030;
  }
  
  .menu-icon-wrapper {
    background-color: #262626;
  }
}
