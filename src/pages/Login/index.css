.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.login-header h1 {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
  color: white;
}

.login-header p {
  font-size: 16px;
  opacity: 0.9;
  color: white;
}

.login-form {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.login-footer {
  margin-top: 24px;
  text-align: center;
}

/* 自定义Tabs样式 */
.adm-tabs-header {
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 24px;
}

.adm-tabs-tab {
  font-size: 16px;
  font-weight: 500;
}

/* 表单项间距 */
.adm-form-item {
  margin-bottom: 20px;
}

/* 输入框样式 */
.adm-input {
  font-size: 16px;
  padding: 12px;
}

/* 按钮样式 */
.adm-button {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}
