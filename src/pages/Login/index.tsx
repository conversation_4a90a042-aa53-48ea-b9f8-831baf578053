import React, {useState} from 'react';
import {Button, Form, Input, Space, Tabs, Toast} from 'antd-mobile';
import {useNavigate} from 'react-router-dom';
import {login} from '../../services/auth';
import './index.css';

interface LoginForm {
  loginValue: string;
  password: string;
}

const LoginPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [loginType, setLoginType] = useState<'username' | 'phone'>('username');
  const navigate = useNavigate();

  const handleSubmit = async (values: LoginForm) => {
    setLoading(true);
    try {
      const response = await login({
        loginType,
        loginValue: values.loginValue,
        password: values.password,
      });

      if (response && response.code === 200) {
        // 保存token到localStorage
        localStorage.setItem('token', response.data.token);

        Toast.show('登录成功');
        navigate('/home');
      } else {
        Toast.show(response?.message || '登录失败');
      }
    } catch (error) {
      console.error('登录异常:', error);
      Toast.show('登录失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div className="login-header">
        <h1>欢迎登录</h1>
        <p>请选择登录方式</p>
      </div>

      <div className="login-form">
        <Tabs
          activeKey={loginType}
          onChange={(key) => setLoginType(key as 'username' | 'phone')}
        >
          <Tabs.Tab key="username" title="用户名登录">
            <Form
              onFinish={handleSubmit}
              footer={
                <Button
                  block
                  type="submit"
                  color="primary"
                  loading={loading}
                  size="large"
                >
                  登录
                </Button>
              }
            >
              <Form.Item
                name="loginValue"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名至少3个字符' },
                ]}
              >
                <Input
                  placeholder="请输入用户名"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码至少6个字符' },
                ]}
              >
                <Input
                  type="password"
                  placeholder="请输入密码"
                />
              </Form.Item>
            </Form>
          </Tabs.Tab>

          <Tabs.Tab key="phone" title="手机号登录">
            <Form
              onFinish={handleSubmit}
              footer={
                <Button
                  block
                  type="submit"
                  color="primary"
                  loading={loading}
                  size="large"
                >
                  登录
                </Button>
              }
            >
              <Form.Item
                name="loginValue"
                rules={[
                  { required: true, message: '请输入手机号' },
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
                ]}
              >
                <Input
                  placeholder="请输入手机号"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码至少6个字符' },
                ]}
              >
                <Input
                  type="password"
                  placeholder="请输入密码"
                />
              </Form.Item>
            </Form>
          </Tabs.Tab>
        </Tabs>

        <div className="login-footer">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button
              block
              fill="outline"
              onClick={() => navigate('/register')}
            >
              还没有账号？立即注册
            </Button>
            <Button
              block
              fill="none"
              size="small"
              onClick={() => {
                Toast.show('忘记密码功能开发中');
              }}
            >
              忘记密码？
            </Button>
          </Space>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
