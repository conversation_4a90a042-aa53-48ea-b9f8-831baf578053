import React, {useState} from 'react';
import {Button, Form, Input, Space, Toast} from 'antd-mobile';
import {useNavigate} from 'react-router-dom';
import {register} from '../../services/auth';
import './index.css';

interface RegisterForm {
  username: string;
  password: string;
  confirmPassword: string;
  nickname: string;
  phone?: string;
  email?: string;
}

const RegisterPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (values: RegisterForm) => {
    if (values.password !== values.confirmPassword) {
      Toast.show('两次输入的密码不一致');
      return;
    }

    setLoading(true);
    try {
      const response = await register({
        username: values.username,
        password: values.password,
        nickname: values.nickname,
        phone: values.phone,
        email: values.email,
      });

      if (response && response.code === 200) {
        Toast.show('注册成功，请登录');
        navigate('/login');
      } else {
        Toast.show(response?.message || '注册失败');
      }
    } catch (error) {
      console.error('注册异常:', error);
      Toast.show('注册失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="register-container">
      <div className="register-header">
        <h1>创建账号</h1>
        <p>填写以下信息完成注册</p>
      </div>

      <div className="register-form">
        <Form
          onFinish={handleSubmit}
          footer={
            <Button
              block
              type="submit"
              color="primary"
              loading={loading}
              size="large"
            >
              注册
            </Button>
          }
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' },
              { max: 20, message: '用户名最多20个字符' },
              { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' },
            ]}
          >
            <Input
              placeholder="请输入用户名"
            />
          </Form.Item>

          <Form.Item
            name="nickname"
            rules={[
              { required: true, message: '请输入昵称' },
              { min: 2, message: '昵称至少2个字符' },
              { max: 20, message: '昵称最多20个字符' },
            ]}
          >
            <Input
              placeholder="请输入昵称"
            />
          </Form.Item>

          <Form.Item
            name="phone"
            rules={[
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
            ]}
          >
            <Input
              placeholder="请输入手机号（可选）"
            />
          </Form.Item>

          <Form.Item
            name="email"
            rules={[
              { type: 'email', message: '请输入正确的邮箱地址' },
            ]}
          >
            <Input
              placeholder="请输入邮箱（可选）"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6个字符' },
              { max: 20, message: '密码最多20个字符' },
            ]}
          >
            <Input
              type="password"
              placeholder="请输入密码"
            />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            rules={[
              { required: true, message: '请确认密码' },
            ]}
          >
            <Input
              type="password"
              placeholder="请确认密码"
            />
          </Form.Item>
        </Form>

        <div className="register-footer">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button
              block
              fill="outline"
              onClick={() => navigate('/login')}
            >
              已有账号？立即登录
            </Button>
          </Space>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;
