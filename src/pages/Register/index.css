.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
}

.register-header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.register-header h1 {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
  color: white;
}

.register-header p {
  font-size: 16px;
  opacity: 0.9;
  color: white;
}

.register-form {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-height: 80vh;
  overflow-y: auto;
}

.register-footer {
  margin-top: 24px;
  text-align: center;
}

/* 表单项间距 */
.adm-form-item {
  margin-bottom: 16px;
}

/* 输入框样式 */
.adm-input {
  font-size: 16px;
  padding: 12px;
}

/* 按钮样式 */
.adm-button {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}
