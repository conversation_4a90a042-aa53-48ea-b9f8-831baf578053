.app-layout {
  min-height: 100vh;
  background: #f8fafc;
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
}

.layout-content {
  padding-bottom: 80px; /* 为底部导航留出空间 */
}

.layout-bottom-spacer {
  height: 24px;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .layout-content {
    padding-bottom: 75px;
  }
}

/* 滚动优化 */
.app-layout {
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .layout-content {
    padding-left: max(16px, env(safe-area-inset-left));
    padding-right: max(16px, env(safe-area-inset-right));
  }
}
