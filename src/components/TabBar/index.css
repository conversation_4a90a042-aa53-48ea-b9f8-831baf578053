.custom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.tab-icon {
  font-size: 22px;
  display: block;
  margin-bottom: 2px;
}

/* 自定义TabBar样式 */
.custom-tab-bar .adm-tab-bar {
  background: white;
  padding: 8px 0 calc(8px + env(safe-area-inset-bottom));
}

.custom-tab-bar .adm-tab-bar-item {
  padding: 4px 8px;
}

.custom-tab-bar .adm-tab-bar-item-title {
  font-size: 12px;
  font-weight: 500;
  margin-top: 2px;
}

.custom-tab-bar .adm-tab-bar-item-icon {
  margin-bottom: 2px;
}

/* 激活状态样式 */
.custom-tab-bar .adm-tab-bar-item.adm-tab-bar-item-active .tab-icon {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

.custom-tab-bar .adm-tab-bar-item.adm-tab-bar-item-active .adm-tab-bar-item-title {
  font-weight: 600;
}
