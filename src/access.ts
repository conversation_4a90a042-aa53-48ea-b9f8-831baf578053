/**
 * UmiJS 权限控制函数
 * @param initialState 初始状态
 * @returns 权限对象
 */
export default function access(initialState: { currentUser?: API.User }) {
  const { currentUser } = initialState || {};

  // 获取用户所有权限
  const userPermissions = new Set<string>();
  currentUser?.roles?.forEach(role => {
    // 处理新的数据结构：权限是字符串数组
    if (Array.isArray(role.permissions)) {
      role.permissions.forEach(permissionKey => {
        if (permissionKey && typeof permissionKey === 'string') {
          userPermissions.add(permissionKey);
        }
      });
    } else if (role.permissions) {
      // 兼容旧的数据结构：权限是对象数组
      role.permissions.forEach((permission: any) => {
        if (permission && permission.permissionKey) {
          userPermissions.add(permission.permissionKey);
        }
      });
    }
  });

  // 权限检查函数
  const hasPermission = (permissionKey: string): boolean => {
    return userPermissions.has(permissionKey);
  };

  // 多权限检查函数
  const hasAnyPermission = (permissionKeys: string[]): boolean => {
    return permissionKeys.some(key => userPermissions.has(key));
  };

  const hasAllPermissions = (permissionKeys: string[]): boolean => {
    return permissionKeys.every(key => userPermissions.has(key));
  };

  // 动态构建权限对象
  const accessObj: Record<string, any> = {
    // 基础权限
    canLogin: !currentUser,
    canAdmin: !!currentUser,

    // 权限检查函数
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
  };

  // 动态添加用户的具体权限
  userPermissions.forEach(permissionKey => {
    accessObj[permissionKey] = true;
  });

  return accessObj;
}
