<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sd.admin.mapper.SysPermissionMapper">

    <!-- 根据用户ID查询权限列表 -->
    <select id="selectPermissionsByUserId" resultType="com.sd.admin.entity.SysPermission">
        SELECT DISTINCT p.*
        FROM sys_permission p
        INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
        INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND p.deleted = 0
          AND p.status = 0
        ORDER BY p.sort ASC
    </select>

    <!-- 根据角色ID查询权限列表 -->
    <select id="selectPermissionsByRoleId" resultType="com.sd.admin.entity.SysPermission">
        SELECT p.*
        FROM sys_permission p
        INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
        WHERE rp.role_id = #{roleId}
          AND p.deleted = 0
          AND p.status = 0
        ORDER BY p.sort ASC
    </select>

    <!-- 根据权限标识查询权限 -->
    <select id="selectByPermissionKey" resultType="com.sd.admin.entity.SysPermission">
        SELECT *
        FROM sys_permission
        WHERE permission_key = #{permissionKey}
          AND deleted = 0
        LIMIT 1
    </select>

    <!-- 检查权限名称是否存在 -->
    <select id="checkPermissionNameUnique" resultType="int">
        SELECT COUNT(1)
        FROM sys_permission
        WHERE permission_name = #{permissionName}
          AND parent_id = #{parentId}
          AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查权限标识是否存在 -->
    <select id="checkPermissionKeyUnique" resultType="int">
        SELECT COUNT(1)
        FROM sys_permission
        WHERE permission_key = #{permissionKey}
          AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 查询子权限数量 -->
    <select id="selectChildrenCount" resultType="int">
        SELECT COUNT(1)
        FROM sys_permission
        WHERE parent_id = #{parentId}
          AND deleted = 0
    </select>

</mapper>
