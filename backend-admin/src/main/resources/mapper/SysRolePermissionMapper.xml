<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sd.admin.mapper.SysRolePermissionMapper">

    <!-- 批量插入角色权限关联 -->
    <insert id="batchInsert">
        INSERT INTO sys_role_permission (role_id, permission_id, create_time, update_time, create_by, update_by, deleted)
        VALUES
        <foreach collection="sysRolePermissions" item="item" separator=",">
            (#{item.roleId}, #{item.permissionId}, NOW(), NOW(), #{item.createBy}, #{item.updateBy}, 0)
        </foreach>
    </insert>

    <!-- 根据角色ID删除角色权限关联 -->
    <delete id="deleteByRoleId">
        UPDATE sys_role_permission
        SET deleted = 1, update_time = NOW()
        WHERE role_id = #{roleId}
          AND deleted = 0
    </delete>

    <!-- 根据权限ID删除角色权限关联 -->
    <delete id="deleteByPermissionId">
        UPDATE sys_role_permission
        SET deleted = 1, update_time = NOW()
        WHERE permission_id = #{permissionId}
          AND deleted = 0
    </delete>

    <!-- 根据角色ID查询权限ID列表 -->
    <select id="selectPermissionIdsByRoleId" resultType="java.lang.Long">
        SELECT permission_id
        FROM sys_role_permission
        WHERE role_id = #{roleId}
          AND deleted = 0
    </select>

    <!-- 根据权限ID查询角色ID列表 -->
    <select id="selectRoleIdsByPermissionId" resultType="java.lang.Long">
        SELECT role_id
        FROM sys_role_permission
        WHERE permission_id = #{permissionId}
          AND deleted = 0
    </select>

</mapper>
