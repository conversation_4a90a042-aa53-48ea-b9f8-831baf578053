<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sd.admin.mapper.SysUserMapper">

    <!-- 根据用户名查询用户 -->
    <select id="selectByUsername" resultType="com.sd.admin.entity.SysUser">
        SELECT *
        FROM sys_user
        WHERE username = #{username}
          AND deleted = 0
        LIMIT 1
    </select>

    <!-- 根据邮箱查询用户 -->
    <select id="selectByEmail" resultType="com.sd.admin.entity.SysUser">
        SELECT *
        FROM sys_user
        WHERE email = #{email}
          AND deleted = 0
        LIMIT 1
    </select>

    <!-- 根据手机号查询用户 -->
    <select id="selectByPhone" resultType="com.sd.admin.entity.SysUser">
        SELECT *
        FROM sys_user
        WHERE phone = #{phone}
          AND deleted = 0
        LIMIT 1
    </select>

    <!-- 检查用户名是否存在 -->
    <select id="checkUsernameUnique" resultType="int">
        SELECT COUNT(1)
        FROM sys_user
        WHERE username = #{username}
          AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查邮箱是否存在 -->
    <select id="checkEmailUnique" resultType="int">
        SELECT COUNT(1)
        FROM sys_user
        WHERE email = #{email}
          AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查手机号是否存在 -->
    <select id="checkPhoneUnique" resultType="int">
        SELECT COUNT(1)
        FROM sys_user
        WHERE phone = #{phone}
          AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

</mapper>
