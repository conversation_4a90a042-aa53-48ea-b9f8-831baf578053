<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sd.admin.mapper.SysUserRoleMapper">

    <!-- 批量插入用户角色关联 -->
    <insert id="batchInsert">
        INSERT INTO sys_user_role (user_id, role_id, create_time, update_time, create_by, update_by, deleted)
        VALUES
        <foreach collection="sysUserRoles" item="item" separator=",">
            (#{item.userId}, #{item.roleId}, NOW(), NOW(), #{item.createBy}, #{item.updateBy}, 0)
        </foreach>
    </insert>

    <!-- 根据用户ID删除用户角色关联 -->
    <delete id="deleteByUserId">
        UPDATE sys_user_role
        SET deleted = 1, update_time = NOW()
        WHERE user_id = #{userId}
          AND deleted = 0
    </delete>

    <!-- 根据角色ID删除用户角色关联 -->
    <delete id="deleteByRoleId">
        UPDATE sys_user_role
        SET deleted = 1, update_time = NOW()
        WHERE role_id = #{roleId}
          AND deleted = 0
    </delete>

    <!-- 根据用户ID查询角色ID列表 -->
    <select id="selectRoleIdsByUserId" resultType="java.lang.Long">
        SELECT role_id
        FROM sys_user_role
        WHERE user_id = #{userId}
          AND deleted = 0
    </select>

    <!-- 根据角色ID查询用户ID列表 -->
    <select id="selectUserIdsByRoleId" resultType="java.lang.Long">
        SELECT user_id
        FROM sys_user_role
        WHERE role_id = #{roleId}
          AND deleted = 0
    </select>

</mapper>
