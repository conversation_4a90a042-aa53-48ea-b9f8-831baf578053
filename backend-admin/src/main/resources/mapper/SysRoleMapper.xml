<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sd.admin.mapper.SysRoleMapper">

    <!-- 根据用户ID查询角色列表 -->
    <select id="selectRolesByUserId" resultType="com.sd.admin.entity.SysRole">
        SELECT r.*
        FROM sys_role r
        INNER JOIN sys_user_role ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND r.deleted = 0
          AND r.status = 0
        ORDER BY r.sort ASC
    </select>

    <!-- 根据角色标识查询角色 -->
    <select id="selectByRoleKey" resultType="com.sd.admin.entity.SysRole">
        SELECT *
        FROM sys_role
        WHERE role_key = #{roleKey}
          AND deleted = 0
        LIMIT 1
    </select>

    <!-- 检查角色名称是否存在 -->
    <select id="checkRoleNameUnique" resultType="int">
        SELECT COUNT(1)
        FROM sys_role
        WHERE role_name = #{roleName}
          AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查角色标识是否存在 -->
    <select id="checkRoleKeyUnique" resultType="int">
        SELECT COUNT(1)
        FROM sys_role
        WHERE role_key = #{roleKey}
          AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

</mapper>
