-- 权限管理系统数据库初始化脚本

-- 创建用户表
CREATE TABLE IF NOT EXISTS `sys_user` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `username` varchar(30) NOT NULL COMMENT '用户名',
    `password` varchar(100) NOT NULL COMMENT '密码',
    `nickname` varchar(30) NOT NULL COMMENT '昵称',
    `email` varchar(50) DEFAULT NULL COMMENT '邮箱',
    `phone` varchar(11) DEFAULT NULL COMMENT '手机号',
    `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` bigint DEFAULT NULL COMMENT '创建人',
    `update_by` bigint DEFAULT NULL COMMENT '更新人',
    `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除（0: 未删除, 1: 已删除）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_email` (`email`),
    UNIQUE KEY `uk_phone` (`phone`),
    KEY `idx_status` (`status`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 创建角色表
CREATE TABLE IF NOT EXISTS `sys_role` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    `role_name` varchar(30) NOT NULL COMMENT '角色名称',
    `role_key` varchar(100) NOT NULL COMMENT '角色标识',
    `description` varchar(500) DEFAULT NULL COMMENT '角色描述',
    `sort` int NOT NULL DEFAULT '0' COMMENT '显示顺序',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
    `data_scope` tinyint NOT NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限 5：仅本人数据权限）',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` bigint DEFAULT NULL COMMENT '创建人',
    `update_by` bigint DEFAULT NULL COMMENT '更新人',
    `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除（0: 未删除, 1: 已删除）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_key` (`role_key`),
    KEY `idx_role_name` (`role_name`),
    KEY `idx_status` (`status`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 创建权限表
CREATE TABLE IF NOT EXISTS `sys_permission` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '权限ID',
    `permission_name` varchar(50) NOT NULL COMMENT '权限名称',
    `permission_key` varchar(100) NOT NULL COMMENT '权限标识',
    `type` tinyint NOT NULL DEFAULT '1' COMMENT '权限类型（1目录 2菜单 3按钮）',
    `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '父权限ID',
    `path` varchar(200) DEFAULT NULL COMMENT '路由地址',
    `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
    `icon` varchar(100) DEFAULT NULL COMMENT '权限图标',
    `sort` int NOT NULL DEFAULT '0' COMMENT '显示顺序',
    `is_frame` tinyint NOT NULL DEFAULT '0' COMMENT '是否为外链（0否 1是）',
    `is_cache` tinyint NOT NULL DEFAULT '0' COMMENT '是否缓存（0缓存 1不缓存）',
    `visible` tinyint NOT NULL DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '权限状态（0正常 1停用）',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` bigint DEFAULT NULL COMMENT '创建人',
    `update_by` bigint DEFAULT NULL COMMENT '更新人',
    `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除（0: 未删除, 1: 已删除）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_permission_key` (`permission_key`),
    KEY `idx_permission_name` (`permission_name`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_type` (`type`),
    KEY `idx_status` (`status`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- 创建用户角色关联表
CREATE TABLE IF NOT EXISTS `sys_user_role` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` bigint NOT NULL COMMENT '用户ID',
    `role_id` bigint NOT NULL COMMENT '角色ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` bigint DEFAULT NULL COMMENT '创建人',
    `update_by` bigint DEFAULT NULL COMMENT '更新人',
    `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除（0: 未删除, 1: 已删除）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_role_id` (`role_id`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- 创建角色权限关联表
CREATE TABLE IF NOT EXISTS `sys_role_permission` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `role_id` bigint NOT NULL COMMENT '角色ID',
    `permission_id` bigint NOT NULL COMMENT '权限ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` bigint DEFAULT NULL COMMENT '创建人',
    `update_by` bigint DEFAULT NULL COMMENT '更新人',
    `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除（0: 未删除, 1: 已删除）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
    KEY `idx_role_id` (`role_id`),
    KEY `idx_permission_id` (`permission_id`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- 插入初始角色数据
INSERT INTO `sys_role` (`id`, `role_name`, `role_key`, `description`, `sort`, `status`, `data_scope`, `remark`) VALUES
(1, '超级管理员', 'super_admin', '超级管理员角色，拥有所有权限', 1, 0, 1, '超级管理员'),
(2, '管理员', 'admin', '管理员角色，拥有大部分权限', 2, 0, 1, '管理员'),
(3, '普通用户', 'user', '普通用户角色，拥有基本权限', 3, 0, 5, '普通用户');

-- 插入初始权限数据
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_key`, `type`, `parent_id`, `path`, `component`, `icon`, `sort`, `is_frame`, `is_cache`, `visible`, `status`, `remark`) VALUES
-- 系统管理
(1, '系统管理', 'system', 1, 0, '/system', NULL, 'SettingOutlined', 1, 0, 0, 0, 0, '系统管理目录'),
(2, '用户管理', 'system:user', 2, 1, '/system/user', 'system/user/index', 'UserOutlined', 1, 0, 0, 0, 0, '用户管理菜单'),
(3, '角色管理', 'system:role', 2, 1, '/system/role', 'system/role/index', 'TeamOutlined', 2, 0, 0, 0, 0, '角色管理菜单'),
(4, '权限管理', 'system:permission', 2, 1, '/system/permission', 'system/permission/index', 'SafetyOutlined', 3, 0, 0, 0, 0, '权限管理菜单'),

-- 用户管理按钮权限
(11, '用户查询', 'system:user:view', 3, 2, NULL, NULL, NULL, 1, 0, 0, 0, 0, '用户查询按钮'),
(12, '用户新增', 'system:user:add', 3, 2, NULL, NULL, NULL, 2, 0, 0, 0, 0, '用户新增按钮'),
(13, '用户修改', 'system:user:edit', 3, 2, NULL, NULL, NULL, 3, 0, 0, 0, 0, '用户修改按钮'),
(14, '用户删除', 'system:user:delete', 3, 2, NULL, NULL, NULL, 4, 0, 0, 0, 0, '用户删除按钮'),
(15, '重置密码', 'system:user:resetPassword', 3, 2, NULL, NULL, NULL, 5, 0, 0, 0, 0, '重置密码按钮'),

-- 角色管理按钮权限
(21, '角色查询', 'system:role:view', 3, 3, NULL, NULL, NULL, 1, 0, 0, 0, 0, '角色查询按钮'),
(22, '角色新增', 'system:role:add', 3, 3, NULL, NULL, NULL, 2, 0, 0, 0, 0, '角色新增按钮'),
(23, '角色修改', 'system:role:edit', 3, 3, NULL, NULL, NULL, 3, 0, 0, 0, 0, '角色修改按钮'),
(24, '角色删除', 'system:role:delete', 3, 3, NULL, NULL, NULL, 4, 0, 0, 0, 0, '角色删除按钮'),
(25, '分配权限', 'system:role:assignPermission', 3, 3, NULL, NULL, NULL, 5, 0, 0, 0, 0, '分配权限按钮'),

-- 权限管理按钮权限
(31, '权限查询', 'system:permission:view', 3, 4, NULL, NULL, NULL, 1, 0, 0, 0, 0, '权限查询按钮'),
(32, '权限新增', 'system:permission:add', 3, 4, NULL, NULL, NULL, 2, 0, 0, 0, 0, '权限新增按钮'),
(33, '权限修改', 'system:permission:edit', 3, 4, NULL, NULL, NULL, 3, 0, 0, 0, 0, '权限修改按钮'),
(34, '权限删除', 'system:permission:delete', 3, 4, NULL, NULL, NULL, 4, 0, 0, 0, 0, '权限删除按钮');

-- 为超级管理员角色分配所有权限
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`)
SELECT 1, id FROM `sys_permission` WHERE `deleted` = 0;

-- 为管理员角色分配部分权限（除了用户删除）
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`) VALUES
(2, 1), (2, 2), (2, 3), (2, 4),
(2, 11), (2, 12), (2, 13), (2, 15),
(2, 21), (2, 22), (2, 23), (2, 25),
(2, 31), (2, 32), (2, 33);

-- 为普通用户角色分配基本权限（只有查询权限）
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`) VALUES
(3, 1), (3, 2), (3, 3), (3, 4),
(3, 11), (3, 21), (3, 31);

-- 插入初始用户数据（密码为123456，使用BCrypt加密）
INSERT INTO `sys_user` (`id`, `username`, `password`, `nickname`, `email`, `phone`, `status`) VALUES
(1, 'admin', '$2a$10$7JB720yubVSOfvVWbazBuOWWZHrOqImqwawBM07vYPfxPWO.9WSeC', '超级管理员', '<EMAIL>', '13800138000', 0),
(2, 'manager', '$2a$10$7JB720yubVSOfvVWbazBuOWWZHrOqImqwawBM07vYPfxPWO.9WSeC', '管理员', '<EMAIL>', '13800138001', 0),
(3, 'user', '$2a$10$7JB720yubVSOfvVWbazBuOWWZHrOqImqwawBM07vYPfxPWO.9WSeC', '普通用户', '<EMAIL>', '13800138002', 0);

-- 为用户分配角色
INSERT INTO `sys_user_role` (`user_id`, `role_id`) VALUES
(1, 1), -- admin 用户分配超级管理员角色
(2, 2), -- manager 用户分配管理员角色
(3, 3); -- user 用户分配普通用户角色
