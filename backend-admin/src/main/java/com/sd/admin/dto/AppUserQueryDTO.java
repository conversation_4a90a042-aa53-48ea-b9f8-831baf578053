package com.sd.admin.dto;

import com.sd.common.api.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户端用户查询DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户端用户查询参数")
public class AppUserQueryDTO extends PageRequest {

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "邀请码")
    private String inviteCode;
}
