package com.sd.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 权限更新DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "权限更新DTO")
public class SysPermissionUpdateDTO {

    /**
     * 权限ID
     */
    @NotNull(message = "权限ID不能为空")
    @Schema(description = "权限ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    /**
     * 权限名称
     */
    @NotBlank(message = "权限名称不能为空")
    @Schema(description = "权限名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String permissionName;

    /**
     * 权限标识
     */
    @NotBlank(message = "权限标识不能为空")
    @Schema(description = "权限标识", requiredMode = Schema.RequiredMode.REQUIRED)
    private String permissionKey;

    /**
     * 权限类型（1目录 2菜单 3按钮）
     */
    @NotNull(message = "权限类型不能为空")
    @Schema(description = "权限类型（1目录 2菜单 3按钮）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer type;

    /**
     * 父权限ID
     */
    @Schema(description = "父权限ID")
    private Long parentId;

    /**
     * 路由地址
     */
    @Schema(description = "路由地址")
    private String path;

    /**
     * 组件路径
     */
    @Schema(description = "组件路径")
    private String component;

    /**
     * 权限图标
     */
    @Schema(description = "权限图标")
    private String icon;

    /**
     * 显示顺序
     */
    @NotNull(message = "显示顺序不能为空")
    @Schema(description = "显示顺序", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer sort;

    /**
     * 是否为外链（0否 1是）
     */
    @Schema(description = "是否为外链（0否 1是）")
    private Integer isFrame;

    /**
     * 是否缓存（0缓存 1不缓存）
     */
    @Schema(description = "是否缓存（0缓存 1不缓存）")
    private Integer isCache;

    /**
     * 菜单状态（0显示 1隐藏）
     */
    @Schema(description = "菜单状态（0显示 1隐藏）")
    private Integer visible;

    /**
     * 权限状态（0正常 1停用）
     */
    @Schema(description = "权限状态（0正常 1停用）")
    private Integer status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}
