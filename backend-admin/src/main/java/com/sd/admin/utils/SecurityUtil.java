package com.sd.admin.utils;

import cn.dev33.satoken.exception.NotWebContextException;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.util.StringUtils;

/**
 * 安全工具类
 *
 * <AUTHOR>
 */
public class SecurityUtil {

    /**
     * 获取当前登录用户ID
     *
     * @return 用户ID
     */
    public static Long getCurrentUserId() {
        try {
            if (!StpUtil.isLogin()) {
                return null;
            }
            String loginIdStr = StpUtil.getLoginIdAsString();
            if (!StringUtils.hasText(loginIdStr)) {
                return null;
            }
            try {
                return Long.parseLong(loginIdStr);
            } catch (NumberFormatException e) {
                return null;
            }
        } catch (NotWebContextException e) {
            // 在非Web上下文中，返回null
            return null;
        }
    }

    /**
     * 获取当前登录用户名
     *
     * @return 用户名
     */
    public static String getCurrentUsername() {
        if (!StpUtil.isLogin()) {
            return null;
        }
        return (String) StpUtil.getSession().get("username");
    }

    /**
     * 是否为管理员
     *
     * @return 是否为管理员
     */
    public static boolean isAdmin() {
        return StpUtil.hasRole("admin");
    }

    /**
     * 是否有权限
     *
     * @param permission 权限标识
     * @return 是否有权限
     */
    public static boolean hasPermission(String permission) {
        return StpUtil.hasPermission(permission);
    }
}
