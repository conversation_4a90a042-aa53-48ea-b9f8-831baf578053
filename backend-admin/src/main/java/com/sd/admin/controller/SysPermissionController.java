package com.sd.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sd.admin.common.constant.PermissionConstants;
import com.sd.admin.dto.SysPermissionCreateDTO;
import com.sd.admin.dto.SysPermissionUpdateDTO;
import com.sd.admin.service.SysPermissionService;
import com.sd.admin.vo.SysPermissionVO;
import com.sd.common.api.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 权限管理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/permission")
@RequiredArgsConstructor
@Tag(name = "权限管理", description = "权限管理相关接口")
public class SysPermissionController {

    private final SysPermissionService sysPermissionService;

    /**
     * 查询权限树
     */
    @GetMapping("/tree")
    @SaCheckPermission(PermissionConstants.PERMISSION_VIEW)
    @Operation(summary = "查询权限树")
    public R<List<SysPermissionVO>> getPermissionTree() {
        List<SysPermissionVO> result = sysPermissionService.getPermissionTree();
        return R.ok(result);
    }

    /**
     * 根据ID查询权限详情
     */
    @GetMapping("/{id}")
    @SaCheckPermission(PermissionConstants.PERMISSION_VIEW)
    @Operation(summary = "根据ID查询权限详情")
    @Parameter(name = "id", description = "权限ID", required = true)
    public R<SysPermissionVO> getDetail(@PathVariable Long id) {
        SysPermissionVO result = sysPermissionService.getDetail(id);
        return R.ok(result);
    }

    /**
     * 创建权限
     */
    @PostMapping
    @SaCheckPermission(PermissionConstants.PERMISSION_ADD)
    @Operation(summary = "创建权限")
    public R<Long> create(@Valid @RequestBody SysPermissionCreateDTO createDTO) {
        Long id = sysPermissionService.create(createDTO);
        return R.ok("创建成功", id);
    }

    /**
     * 更新权限
     */
    @PutMapping
    @SaCheckPermission(PermissionConstants.PERMISSION_EDIT)
    @Operation(summary = "更新权限")
    public R<Void> update(@Valid @RequestBody SysPermissionUpdateDTO updateDTO) {
        sysPermissionService.update(updateDTO);
        return R.ok();
    }

    /**
     * 删除权限
     */
    @DeleteMapping("/{id}")
    @SaCheckPermission(PermissionConstants.PERMISSION_DELETE)
    @Operation(summary = "删除权限")
    @Parameter(name = "id", description = "权限ID", required = true)
    public R<Void> delete(@PathVariable Long id) {
        sysPermissionService.delete(id);
        return R.ok();
    }
}
