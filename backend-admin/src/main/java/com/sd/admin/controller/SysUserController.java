package com.sd.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import com.sd.admin.common.constant.PermissionConstants;
import com.sd.admin.dto.SysUserCreateDTO;
import com.sd.admin.dto.SysUserPasswordResetDTO;
import com.sd.admin.dto.SysUserQueryDTO;
import com.sd.admin.dto.SysUserUpdateDTO;
import com.sd.admin.service.SysUserService;
import com.sd.admin.vo.SysUserVO;
import com.sd.common.api.PageResult;
import com.sd.common.api.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统用户管理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sys-user")
@RequiredArgsConstructor
@Tag(name = "系统用户管理", description = "系统用户管理相关接口")
public class SysUserController {

    private final SysUserService sysUserService;

    /**
     * 分页查询用户列表
     */
    @GetMapping("/page")
    @SaCheckPermission(PermissionConstants.USER_VIEW)
    @Operation(summary = "分页查询用户列表")
    public R<PageResult<SysUserVO>> page(@Valid SysUserQueryDTO queryDTO) {
        PageResult<SysUserVO> result = sysUserService.page(queryDTO);
        return R.ok(result);
    }

    /**
     * 根据ID查询用户详情
     */
    @GetMapping("/{id}")
    @SaCheckPermission(PermissionConstants.USER_VIEW)
    @Operation(summary = "根据ID查询用户详情")
    @Parameter(name = "id", description = "用户ID", required = true)
    public R<SysUserVO> getDetail(@PathVariable Long id) {
        SysUserVO result = sysUserService.getDetail(id);
        return R.ok(result);
    }

    /**
     * 创建用户
     */
    @PostMapping
    @SaCheckPermission(PermissionConstants.USER_ADD)
    @Operation(summary = "创建用户")
    public R<Long> create(@Valid @RequestBody SysUserCreateDTO createDTO) {
        Long id = sysUserService.create(createDTO);
        return R.ok("创建成功", id);
    }

    /**
     * 更新用户
     */
    @PutMapping
    @SaCheckPermission(PermissionConstants.USER_EDIT)
    @Operation(summary = "更新用户")
    public R<Void> update(@Valid @RequestBody SysUserUpdateDTO updateDTO) {
        sysUserService.update(updateDTO);
        return R.ok();
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    @SaCheckPermission(PermissionConstants.USER_DELETE)
    @Operation(summary = "删除用户")
    @Parameter(name = "id", description = "用户ID", required = true)
    public R<Void> delete(@PathVariable Long id) {
        sysUserService.delete(id);
        return R.ok();
    }

    /**
     * 批量删除用户
     */
    @DeleteMapping("/batch")
    @SaCheckPermission(PermissionConstants.USER_DELETE)
    @Operation(summary = "批量删除用户")
    public R<Void> batchDelete(@RequestBody List<Long> ids) {
        sysUserService.batchDelete(ids);
        return R.ok();
    }

    /**
     * 重置用户密码
     */
    @PutMapping("/reset-password")
    @SaCheckPermission(PermissionConstants.USER_RESET_PASSWORD)
    @Operation(summary = "重置用户密码")
    public R<Void> resetPassword(@Valid @RequestBody SysUserPasswordResetDTO resetDTO) {
        sysUserService.resetPassword(resetDTO);
        return R.ok();
    }

    /**
     * 获取当前登录用户信息
     */
    @GetMapping("/current")
    @Operation(summary = "获取当前登录用户信息")
    public R<SysUserVO> getCurrentUser() {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            SysUserVO user = sysUserService.getDetail(userId);
            return R.ok("获取用户信息成功", user);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
