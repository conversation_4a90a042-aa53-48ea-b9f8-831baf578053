package com.sd.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sd.admin.common.constant.PermissionConstants;
import com.sd.admin.dto.SysRoleCreateDTO;
import com.sd.admin.dto.SysRoleQueryDTO;
import com.sd.admin.dto.SysRoleUpdateDTO;
import com.sd.admin.service.SysRoleService;
import com.sd.admin.vo.SysRoleVO;
import com.sd.common.api.PageResult;
import com.sd.common.api.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色管理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/role")
@RequiredArgsConstructor
@Tag(name = "角色管理", description = "角色管理相关接口")
public class SysRoleController {

    private final SysRoleService sysRoleService;

    /**
     * 分页查询角色列表
     */
    @GetMapping("/page")
    @SaCheckPermission(PermissionConstants.ROLE_VIEW)
    @Operation(summary = "分页查询角色列表")
    public R<PageResult<SysRoleVO>> page(@Valid SysRoleQueryDTO queryDTO) {
        PageResult<SysRoleVO> result = sysRoleService.page(queryDTO);
        return R.ok(result);
    }

    /**
     * 查询所有角色列表
     */
    @GetMapping("/list")
    @SaCheckPermission(PermissionConstants.ROLE_VIEW)
    @Operation(summary = "查询所有角色列表")
    public R<List<SysRoleVO>> list() {
        List<SysRoleVO> result = sysRoleService.listAll();
        return R.ok(result);
    }

    /**
     * 根据ID查询角色详情
     */
    @GetMapping("/{id}")
    @SaCheckPermission(PermissionConstants.ROLE_VIEW)
    @Operation(summary = "根据ID查询角色详情")
    @Parameter(name = "id", description = "角色ID", required = true)
    public R<SysRoleVO> getDetail(@PathVariable Long id) {
        SysRoleVO result = sysRoleService.getDetail(id);
        return R.ok(result);
    }

    /**
     * 创建角色
     */
    @PostMapping
    @SaCheckPermission(PermissionConstants.ROLE_ADD)
    @Operation(summary = "创建角色")
    public R<Long> create(@Valid @RequestBody SysRoleCreateDTO createDTO) {
        Long id = sysRoleService.create(createDTO);
        return R.ok("创建成功", id);
    }

    /**
     * 更新角色
     */
    @PutMapping
    @SaCheckPermission(PermissionConstants.ROLE_EDIT)
    @Operation(summary = "更新角色")
    public R<Void> update(@Valid @RequestBody SysRoleUpdateDTO updateDTO) {
        sysRoleService.update(updateDTO);
        return R.ok();
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{id}")
    @SaCheckPermission(PermissionConstants.ROLE_DELETE)
    @Operation(summary = "删除角色")
    @Parameter(name = "id", description = "角色ID", required = true)
    public R<Void> delete(@PathVariable Long id) {
        sysRoleService.delete(id);
        return R.ok();
    }

    /**
     * 批量删除角色
     */
    @DeleteMapping("/batch")
    @SaCheckPermission(PermissionConstants.ROLE_DELETE)
    @Operation(summary = "批量删除角色")
    public R<Void> batchDelete(@RequestBody List<Long> ids) {
        sysRoleService.batchDelete(ids);
        return R.ok();
    }
}
