package com.sd.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sd.admin.common.constant.PermissionConstants;
import com.sd.admin.dto.SysUserRoleAssignDTO;
import com.sd.admin.service.SysUserRoleService;
import com.sd.common.api.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户角色管理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user-role")
@RequiredArgsConstructor
@Tag(name = "用户角色管理", description = "用户角色管理相关接口")
public class SysUserRoleController {

    private final SysUserRoleService sysUserRoleService;

    /**
     * 分配用户角色
     */
    @PostMapping("/assign")
    @SaCheckPermission(PermissionConstants.USER_EDIT)
    @Operation(summary = "分配用户角色")
    public R<Void> assignUserRoles(@Valid @RequestBody SysUserRoleAssignDTO assignDTO) {
        sysUserRoleService.assignUserRoles(assignDTO);
        return R.ok();
    }

    /**
     * 根据用户ID查询角色ID列表
     */
    @GetMapping("/user/{userId}")
    @SaCheckPermission(PermissionConstants.USER_VIEW)
    @Operation(summary = "根据用户ID查询角色ID列表")
    @Parameter(name = "userId", description = "用户ID", required = true)
    public R<List<Long>> getRoleIdsByUserId(@PathVariable Long userId) {
        List<Long> result = sysUserRoleService.getRoleIdsByUserId(userId);
        return R.ok(result);
    }

    /**
     * 根据角色ID查询用户ID列表
     */
    @GetMapping("/role/{roleId}")
    @SaCheckPermission(PermissionConstants.ROLE_VIEW)
    @Operation(summary = "根据角色ID查询用户ID列表")
    @Parameter(name = "roleId", description = "角色ID", required = true)
    public R<List<Long>> getUserIdsByRoleId(@PathVariable Long roleId) {
        List<Long> result = sysUserRoleService.getUserIdsByRoleId(roleId);
        return R.ok(result);
    }
}
