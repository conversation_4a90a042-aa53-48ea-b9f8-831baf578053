package com.sd.admin.common.constant;

/**
 * 权限常量类
 *
 * <AUTHOR>
 */
public class PermissionConstants {

    // ==================== 系统管理 ====================
    
    /**
     * 用户管理
     */
    public static final String USER_VIEW = "system:user:view";
    public static final String USER_ADD = "system:user:add";
    public static final String USER_EDIT = "system:user:edit";
    public static final String USER_DELETE = "system:user:delete";
    public static final String USER_EXPORT = "system:user:export";
    public static final String USER_IMPORT = "system:user:import";
    public static final String USER_RESET_PASSWORD = "system:user:resetPassword";
    
    /**
     * 角色管理
     */
    public static final String ROLE_VIEW = "system:role:view";
    public static final String ROLE_ADD = "system:role:add";
    public static final String ROLE_EDIT = "system:role:edit";
    public static final String ROLE_DELETE = "system:role:delete";
    public static final String ROLE_ASSIGN_PERMISSION = "system:role:assignPermission";
    
    /**
     * 权限管理
     */
    public static final String PERMISSION_VIEW = "system:permission:view";
    public static final String PERMISSION_ADD = "system:permission:add";
    public static final String PERMISSION_EDIT = "system:permission:edit";
    public static final String PERMISSION_DELETE = "system:permission:delete";
    
    /**
     * 菜单管理
     */
    public static final String MENU_VIEW = "system:menu:view";
    public static final String MENU_ADD = "system:menu:add";
    public static final String MENU_EDIT = "system:menu:edit";
    public static final String MENU_DELETE = "system:menu:delete";
    
    /**
     * 部门管理
     */
    public static final String DEPT_VIEW = "system:dept:view";
    public static final String DEPT_ADD = "system:dept:add";
    public static final String DEPT_EDIT = "system:dept:edit";
    public static final String DEPT_DELETE = "system:dept:delete";
    
    // ==================== 系统监控 ====================
    
    /**
     * 操作日志
     */
    public static final String LOG_OPERATION_VIEW = "monitor:log:operation:view";
    public static final String LOG_OPERATION_DELETE = "monitor:log:operation:delete";
    public static final String LOG_OPERATION_EXPORT = "monitor:log:operation:export";
    
    /**
     * 登录日志
     */
    public static final String LOG_LOGIN_VIEW = "monitor:log:login:view";
    public static final String LOG_LOGIN_DELETE = "monitor:log:login:delete";
    public static final String LOG_LOGIN_EXPORT = "monitor:log:login:export";
    
    /**
     * 在线用户
     */
    public static final String ONLINE_USER_VIEW = "monitor:online:view";
    public static final String ONLINE_USER_FORCE_LOGOUT = "monitor:online:forceLogout";
    
    // ==================== 系统工具 ====================
    
    /**
     * 代码生成
     */
    public static final String GENERATOR_VIEW = "tool:generator:view";
    public static final String GENERATOR_GENERATE = "tool:generator:generate";
    public static final String GENERATOR_PREVIEW = "tool:generator:preview";
    public static final String GENERATOR_DOWNLOAD = "tool:generator:download";
    
    // ==================== 角色常量 ====================
    
    /**
     * 超级管理员角色
     */
    public static final String ROLE_SUPER_ADMIN = "super_admin";
    
    /**
     * 管理员角色
     */
    public static final String ROLE_ADMIN = "admin";
    
    /**
     * 普通用户角色
     */
    public static final String ROLE_USER = "user";
}
