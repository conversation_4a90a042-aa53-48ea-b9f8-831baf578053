package com.sd.admin.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sd.admin.dto.AppUserPasswordResetDTO;
import com.sd.admin.dto.AppUserQueryDTO;
import com.sd.admin.vo.AppUserVO;
import com.sd.common.api.PageResult;
import com.sd.common.entity.AppUser;
import com.sd.common.mapper.AppUserMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * 用户端用户管理服务（管理后台）
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AppUserManageService extends ServiceImpl<AppUserMapper, AppUser> {

    private final PasswordEncoder passwordEncoder;

    /**
     * 分页查询用户端用户列表
     *
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    public PageResult<AppUserVO> page(AppUserQueryDTO queryDTO) {
        LambdaQueryWrapper<AppUser> queryWrapper = new LambdaQueryWrapper<>();

        // 构建查询条件
        queryWrapper.like(StrUtil.isNotBlank(queryDTO.getUsername()), AppUser::getUsername, queryDTO.getUsername())
                .like(StrUtil.isNotBlank(queryDTO.getNickname()), AppUser::getNickname, queryDTO.getNickname())
                .like(StrUtil.isNotBlank(queryDTO.getPhone()), AppUser::getPhone, queryDTO.getPhone())
                .like(StrUtil.isNotBlank(queryDTO.getEmail()), AppUser::getEmail, queryDTO.getEmail())
                .eq(queryDTO.getStatus() != null, AppUser::getStatus, queryDTO.getStatus())
                .eq(StrUtil.isNotBlank(queryDTO.getInviteCode()), AppUser::getInviteCode, queryDTO.getInviteCode())
                .orderByDesc(AppUser::getCreateTime);

        // 分页查询
        Page<AppUser> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        Page<AppUser> result = this.page(page, queryWrapper);

        // 转换为VO
        PageResult<AppUserVO> pageResult = new PageResult<>();
        pageResult.setPageNum(queryDTO.getPageNum());
        pageResult.setPageSize(queryDTO.getPageSize());
        pageResult.setTotal(result.getTotal());
        pageResult.setList(result.getRecords().stream().map(this::convertToVO).toList());

        return pageResult;
    }

    /**
     * 根据ID查询用户端用户详情
     *
     * @param id 用户ID
     * @return 用户详情
     */
    public AppUserVO getDetail(Long id) {
        AppUser user = this.getById(id);
        Assert.notNull(user, "用户不存在");
        return convertToVO(user);
    }

    /**
     * 重置用户登录密码
     *
     * @param resetDTO 重置参数
     */
    public void resetPassword(AppUserPasswordResetDTO resetDTO) {
        AppUser user = this.getById(resetDTO.getId());
        Assert.notNull(user, "用户不存在");

        user.setPassword(passwordEncoder.encode(resetDTO.getNewPassword()));
        this.updateById(user);
    }

    /**
     * 重置用户支付密码
     *
     * @param resetDTO 重置参数
     */
    public void resetPayPassword(AppUserPasswordResetDTO resetDTO) {
        AppUser user = this.getById(resetDTO.getId());
        Assert.notNull(user, "用户不存在");

        user.setPayPassword(passwordEncoder.encode(resetDTO.getNewPassword()));
        this.updateById(user);
    }

    /**
     * 启用/停用用户
     *
     * @param id 用户ID
     * @param status 状态
     */
    public void changeStatus(Long id, Integer status) {
        AppUser user = this.getById(id);
        Assert.notNull(user, "用户不存在");

        user.setStatus(status);
        this.updateById(user);
    }

    /**
     * 转换为VO
     *
     * @param user 用户实体
     * @return VO
     */
    private AppUserVO convertToVO(AppUser user) {
        AppUserVO vo = new AppUserVO();
        BeanUtils.copyProperties(user, vo);
        vo.setHasPayPassword(StrUtil.isNotBlank(user.getPayPassword()));
        return vo;
    }
}
