package com.sd.admin.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sd.admin.dto.SysUserRoleAssignDTO;
import com.sd.admin.entity.SysUserRole;
import com.sd.admin.mapper.SysUserRoleMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户角色关联服务实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SysUserRoleService extends ServiceImpl<SysUserRoleMapper, SysUserRole> {

    /**
     * 分配用户角色
     *
     * @param assignDTO 分配参数
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"user", "role"}, allEntries = true)
    public void assignUserRoles(SysUserRoleAssignDTO assignDTO) {
        // 删除用户原有角色
        baseMapper.deleteByUserId(assignDTO.getUserId());

        // 分配新角色
        if (!CollectionUtils.isEmpty(assignDTO.getRoleIds())) {
            List<SysUserRole> userRoles = assignDTO.getRoleIds().stream()
                    .map(roleId -> {
                        SysUserRole userRole = new SysUserRole();
                        userRole.setUserId(assignDTO.getUserId());
                        userRole.setRoleId(roleId);
                        return userRole;
                    })
                    .collect(Collectors.toList());
            baseMapper.batchInsert(userRoles);
        }
    }

    /**
     * 根据用户ID查询角色ID列表
     *
     * @param userId 用户ID
     * @return 角色ID列表
     */
    public List<Long> getRoleIdsByUserId(Long userId) {
        return baseMapper.selectRoleIdsByUserId(userId);
    }

    /**
     * 根据角色ID查询用户ID列表
     *
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    public List<Long> getUserIdsByRoleId(Long roleId) {
        return baseMapper.selectUserIdsByRoleId(roleId);
    }

    /**
     * 删除用户角色关联
     *
     * @param userId 用户ID
     */
    @CacheEvict(value = {"user", "role"}, allEntries = true)
    public void deleteByUserId(Long userId) {
        baseMapper.deleteByUserId(userId);
    }

    /**
     * 删除角色用户关联
     *
     * @param roleId 角色ID
     */
    @CacheEvict(value = {"user", "role"}, allEntries = true)
    public void deleteByRoleId(Long roleId) {
        baseMapper.deleteByRoleId(roleId);
    }

    /**
     * 分配用户角色（通过用户ID和角色ID列表）
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"user", "role"}, allEntries = true)
    public void assignUserRoles(Long userId, List<Long> roleIds) {
        // 删除用户原有角色
        baseMapper.deleteByUserId(userId);

        // 分配新角色
        if (!CollectionUtils.isEmpty(roleIds)) {
            List<SysUserRole> userRoles = roleIds.stream()
                    .map(roleId -> {
                        SysUserRole userRole = new SysUserRole();
                        userRole.setUserId(userId);
                        userRole.setRoleId(roleId);
                        return userRole;
                    })
                    .collect(Collectors.toList());
            baseMapper.batchInsert(userRoles);
        }
    }
}
