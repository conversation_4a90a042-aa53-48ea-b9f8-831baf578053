package com.sd.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sd.admin.dto.SysPermissionCreateDTO;
import com.sd.admin.dto.SysPermissionUpdateDTO;
import com.sd.admin.entity.SysPermission;
import com.sd.admin.mapper.SysPermissionMapper;
import com.sd.admin.mapper.SysRolePermissionMapper;
import com.sd.admin.vo.SysPermissionVO;
import com.sd.common.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 权限服务实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SysPermissionService extends ServiceImpl<SysPermissionMapper, SysPermission> {

    private final SysRolePermissionMapper sysRolePermissionMapper;

    /**
     * 查询权限树
     *
     * @return 权限树
     */
    @Cacheable(value = "permission", key = "'tree'")
    public List<SysPermissionVO> getPermissionTree() {
        List<SysPermission> sysPermissions = this.list(new LambdaQueryWrapper<SysPermission>()
                .orderByAsc(SysPermission::getSort));
        
        List<SysPermissionVO> voList = sysPermissions.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        
        return buildTree(voList, 0L);
    }

    /**
     * 根据ID查询权限详情
     *
     * @param id 权限ID
     * @return 权限详情
     */
    @Cacheable(value = "permission", key = "#id")
    public SysPermissionVO getDetail(Long id) {
        SysPermission sysPermission = this.getById(id);
        Assert.notNull(sysPermission, "权限不存在");
        return convertToVO(sysPermission);
    }

    /**
     * 创建权限
     *
     * @param createDTO 创建参数
     * @return 权限ID
     */
    @CacheEvict(value = "permission", allEntries = true)
    public Long create(SysPermissionCreateDTO createDTO) {
        // 检查权限名称是否重复
        checkPermissionNameUnique(createDTO.getPermissionName(), createDTO.getParentId(), null);
        // 检查权限标识是否重复
        checkPermissionKeyUnique(createDTO.getPermissionKey(), null);

        SysPermission sysPermission = new SysPermission();
        BeanUtils.copyProperties(createDTO, sysPermission);
        this.save(sysPermission);

        return sysPermission.getId();
    }

    /**
     * 更新权限
     *
     * @param updateDTO 更新参数
     */
    @CacheEvict(value = "permission", allEntries = true)
    public void update(SysPermissionUpdateDTO updateDTO) {
        SysPermission existSysPermission = this.getById(updateDTO.getId());
        Assert.notNull(existSysPermission, "权限不存在");

        // 检查权限名称是否重复
        checkPermissionNameUnique(updateDTO.getPermissionName(), updateDTO.getParentId(), updateDTO.getId());
        // 检查权限标识是否重复
        checkPermissionKeyUnique(updateDTO.getPermissionKey(), updateDTO.getId());

        SysPermission sysPermission = new SysPermission();
        BeanUtils.copyProperties(updateDTO, sysPermission);
        this.updateById(sysPermission);
    }

    /**
     * 删除权限
     *
     * @param id 权限ID
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public void delete(Long id) {
        SysPermission sysPermission = this.getById(id);
        Assert.notNull(sysPermission, "权限不存在");

        // 检查是否有子权限
        int childrenCount = baseMapper.selectChildrenCount(id);
        Assert.isTrue(childrenCount == 0, "存在子权限，无法删除");

        // 删除权限
        this.removeById(id);
        // 删除角色权限关联
        sysRolePermissionMapper.deleteByPermissionId(id);
    }

    /**
     * 根据用户ID查询权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    public List<SysPermission> getPermissionsByUserId(Long userId) {
        return baseMapper.selectPermissionsByUserId(userId);
    }

    /**
     * 根据角色ID查询权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    public List<SysPermission> getPermissionsByRoleId(Long roleId) {
        return baseMapper.selectPermissionsByRoleId(roleId);
    }

    /**
     * 根据用户ID查询权限标识列表
     *
     * @param userId 用户ID
     * @return 权限标识列表
     */
    public List<String> getPermissionKeysByUserId(Long userId) {
        List<SysPermission> sysPermissions = getPermissionsByUserId(userId);
        return sysPermissions.stream()
                .map(SysPermission::getPermissionKey)
                .collect(Collectors.toList());
    }

    /**
     * 根据用户ID查询菜单权限树
     *
     * @param userId 用户ID
     * @return 菜单权限树
     */
    public List<SysPermissionVO> getMenuTreeByUserId(Long userId) {
        List<SysPermission> sysPermissions = getPermissionsByUserId(userId);
        List<SysPermissionVO> voList = sysPermissions.stream()
                .filter(sysPermission -> sysPermission.getType() != 3) // 排除按钮权限
                .filter(sysPermission -> sysPermission.getStatus() == 0) // 只查询正常状态
                .filter(sysPermission -> sysPermission.getVisible() == 0) // 只查询显示的菜单
                .map(this::convertToVO)
                .collect(Collectors.toList());
        
        return buildTree(voList, 0L);
    }

    /**
     * 构建权限树
     *
     * @param permissions 权限列表
     * @param parentId 父权限ID
     * @return 权限树
     */
    private List<SysPermissionVO> buildTree(List<SysPermissionVO> permissions, Long parentId) {
        List<SysPermissionVO> tree = new ArrayList<>();
        
        Map<Long, List<SysPermissionVO>> groupByParent = permissions.stream()
                .collect(Collectors.groupingBy(SysPermissionVO::getParentId));
        
        List<SysPermissionVO> children = groupByParent.get(parentId);
        if (children != null) {
            for (SysPermissionVO permission : children) {
                permission.setChildren(buildTree(permissions, permission.getId()));
                tree.add(permission);
            }
        }
        
        return tree;
    }

    /**
     * 检查权限名称是否唯一
     *
     * @param permissionName 权限名称
     * @param parentId 父权限ID
     * @param excludeId 排除的权限ID
     */
    private void checkPermissionNameUnique(String permissionName, Long parentId, Long excludeId) {
        int count = baseMapper.checkPermissionNameUnique(permissionName, parentId, excludeId);
        if (count > 0) {
            throw new BusinessException("权限名称已存在");
        }
    }

    /**
     * 检查权限标识是否唯一
     *
     * @param permissionKey 权限标识
     * @param excludeId 排除的权限ID
     */
    private void checkPermissionKeyUnique(String permissionKey, Long excludeId) {
        int count = baseMapper.checkPermissionKeyUnique(permissionKey, excludeId);
        if (count > 0) {
            throw new BusinessException("权限标识已存在");
        }
    }

    /**
     * 转换为VO
     *
     * @param sysPermission 权限实体
     * @return 权限VO
     */
    private SysPermissionVO convertToVO(SysPermission sysPermission) {
        SysPermissionVO vo = new SysPermissionVO();
        BeanUtils.copyProperties(sysPermission, vo);
        return vo;
    }
}
