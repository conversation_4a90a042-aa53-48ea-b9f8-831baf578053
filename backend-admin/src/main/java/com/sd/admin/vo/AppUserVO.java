package com.sd.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户端用户VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "用户端用户信息")
public class AppUserVO {

    @Schema(description = "用户ID")
    private Long id;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "性别（0未知 1男 2女）")
    private Integer gender;

    @Schema(description = "生日")
    private String birthday;

    @Schema(description = "个人简介")
    private String bio;

    @Schema(description = "状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "邀请码")
    private String inviteCode;

    @Schema(description = "是否设置支付密码")
    private Boolean hasPayPassword;

    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;

    @Schema(description = "最后登录IP")
    private String lastLoginIp;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
