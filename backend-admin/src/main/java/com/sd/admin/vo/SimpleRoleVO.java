package com.sd.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 简化角色VO（用于登录和用户信息返回）
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "简化角色信息")
public class SimpleRoleVO {

    /**
     * 角色ID
     */
    @Schema(description = "角色ID")
    private Long id;

    /**
     * 角色名称
     */
    @Schema(description = "角色名称")
    private String roleName;

    /**
     * 角色标识
     */
    @Schema(description = "角色标识")
    private String roleKey;

    /**
     * 权限标识列表
     */
    @Schema(description = "权限标识列表")
    private List<String> permissions;
}
