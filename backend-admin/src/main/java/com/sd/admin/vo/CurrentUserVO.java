package com.sd.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 当前用户信息VO（精简版）
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "当前用户信息")
public class CurrentUserVO {

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long id;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String username;

    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickname;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;

    /**
     * 头像
     */
    @Schema(description = "头像")
    private String avatar;

    /**
     * 角色列表（精简版）
     */
    @Schema(description = "角色列表")
    private List<SimpleRoleVO> roles;
}
