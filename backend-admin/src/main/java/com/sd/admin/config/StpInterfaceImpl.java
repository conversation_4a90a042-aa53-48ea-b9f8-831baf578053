package com.sd.admin.config;

import cn.dev33.satoken.stp.StpInterface;
import com.sd.admin.entity.SysPermission;
import com.sd.admin.entity.SysRole;
import com.sd.admin.service.SysPermissionService;
import com.sd.admin.service.SysRoleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Sa-Token 权限验证接口实现
 * 
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class StpInterfaceImpl implements StpInterface {

    private final SysRoleService sysRoleService;
    private final SysPermissionService sysPermissionService;

    /**
     * 返回一个账号所拥有的权限码集合
     *
     * @param loginId   账号id
     * @param loginType 账号类型
     * @return 该账号所拥有的权限码集合
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        // 根据用户ID查询权限列表
        Long userId = Long.valueOf(loginId.toString());
        List<SysPermission> permissions = sysPermissionService.getPermissionsByUserId(userId);
        
        // 返回权限标识列表
        return permissions.stream()
                .map(SysPermission::getPermissionKey)
                .collect(Collectors.toList());
    }

    /**
     * 返回一个账号所拥有的角色标识集合
     *
     * @param loginId   账号id
     * @param loginType 账号类型
     * @return 该账号所拥有的角色标识集合
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        // 根据用户ID查询角色列表
        Long userId = Long.valueOf(loginId.toString());
        List<SysRole> roles = sysRoleService.getRolesByUserId(userId);
        
        // 返回角色标识列表
        return roles.stream()
                .map(SysRole::getRoleKey)
                .collect(Collectors.toList());
    }
}
