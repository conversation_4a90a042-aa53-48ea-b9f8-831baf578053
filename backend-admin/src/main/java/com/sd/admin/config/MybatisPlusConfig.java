package com.sd.admin.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.sd.admin.utils.SecurityUtil;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

/**
 * MyBatis-Plus配置类
 *
 * <AUTHOR>
 */
@Configuration
public class MybatisPlusConfig {

    /**
     * MyBatis-Plus拦截器
     *
     * @return MyBatis-Plus拦截器
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        // 乐观锁插件
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        // 防止全表更新与删除插件
        interceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());
        return interceptor;
    }

    /**
     * 元对象处理器
     *
     * @return 元对象处理器
     */
    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new MetaObjectHandler() {
            @Override
            public void insertFill(MetaObject metaObject) {
                this.strictInsertFill(metaObject, "createTime", () -> LocalDateTime.now(), LocalDateTime.class);
                this.strictInsertFill(metaObject, "updateTime", () -> LocalDateTime.now(), LocalDateTime.class);
                this.strictInsertFill(metaObject, "deleted", () -> 0, Integer.class);
                this.strictInsertFill(metaObject, "version", () -> 1, Integer.class);
                // 获取当前用户ID
                Long userId = getCurrentUserId();
                if (userId != null) {
                    this.strictInsertFill(metaObject, "createBy", () -> userId, Long.class);
                    this.strictInsertFill(metaObject, "updateBy", () -> userId, Long.class);
                }
            }

            @Override
            public void updateFill(MetaObject metaObject) {
                this.strictUpdateFill(metaObject, "updateTime", () -> LocalDateTime.now(), LocalDateTime.class);
                // 获取当前用户ID
                Long userId = getCurrentUserId();
                if (userId != null) {
                    this.strictUpdateFill(metaObject, "updateBy", () -> userId, Long.class);
                }
            }

            /**
             * 获取当前用户ID
             *
             * @return 当前用户ID
             */
            private Long getCurrentUserId() {
                try {
                    return SecurityUtil.getCurrentUserId();
                } catch (Exception e) {
                    // 在应用启动时可能会出现异常，返回null
                    return null;
                }
            }
        };
    }
}
