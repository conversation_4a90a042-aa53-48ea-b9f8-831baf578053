package com.sd.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sd.admin.entity.SysPermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权限Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface SysPermissionMapper extends BaseMapper<SysPermission> {

    /**
     * 根据用户ID查询权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    List<SysPermission> selectPermissionsByUserId(@Param("userId") Long userId);

    /**
     * 根据角色ID查询权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<SysPermission> selectPermissionsByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据权限标识查询权限
     *
     * @param permissionKey 权限标识
     * @return 权限
     */
    SysPermission selectByPermissionKey(@Param("permissionKey") String permissionKey);

    /**
     * 检查权限名称是否存在
     *
     * @param permissionName 权限名称
     * @param parentId 父权限ID
     * @param excludeId 排除的权限ID
     * @return 数量
     */
    int checkPermissionNameUnique(@Param("permissionName") String permissionName, 
                                  @Param("parentId") Long parentId, 
                                  @Param("excludeId") Long excludeId);

    /**
     * 检查权限标识是否存在
     *
     * @param permissionKey 权限标识
     * @param excludeId 排除的权限ID
     * @return 数量
     */
    int checkPermissionKeyUnique(@Param("permissionKey") String permissionKey, @Param("excludeId") Long excludeId);

    /**
     * 查询子权限数量
     *
     * @param parentId 父权限ID
     * @return 数量
     */
    int selectChildrenCount(@Param("parentId") Long parentId);
}
