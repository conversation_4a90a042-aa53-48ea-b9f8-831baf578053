package com.sd.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sd.admin.entity.SysRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface SysRoleMapper extends BaseMapper<SysRole> {

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<SysRole> selectRolesByUserId(@Param("userId") Long userId);

    /**
     * 根据角色标识查询角色
     *
     * @param roleKey 角色标识
     * @return 角色
     */
    SysRole selectByRoleKey(@Param("roleKey") String roleKey);

    /**
     * 检查角色名称是否存在
     *
     * @param roleName 角色名称
     * @param excludeId 排除的角色ID
     * @return 数量
     */
    int checkRoleNameUnique(@Param("roleName") String roleName, @Param("excludeId") Long excludeId);

    /**
     * 检查角色标识是否存在
     *
     * @param roleKey 角色标识
     * @param excludeId 排除的角色ID
     * @return 数量
     */
    int checkRoleKeyUnique(@Param("roleKey") String roleKey, @Param("excludeId") Long excludeId);
}
