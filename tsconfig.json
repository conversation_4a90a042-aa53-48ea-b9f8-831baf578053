{"extends": "./src/.umi/tsconfig.json", "compilerOptions": {"target": "ES2022", "lib": ["DOM", "DOM.Iterable", "ES2022"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "incremental": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@@/*": ["src/.umi/*"]}}, "include": ["src/**/*", "typings.d.ts"], "exclude": ["node_modules", "dist", ".umi"]}