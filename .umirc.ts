import {defineConfig} from '@umijs/max';

export default defineConfig({
  // 配置主题
  theme: {
    '@primary-color': '#1890ff',
  },
  antd: {
    // 关闭兼容模式，因为使用了 Ant Design 5
    import: false,
    // 使用 less 变量
    style: 'less',
  },
  access: {},
  model: {},
  initialState: {},
  request: {},
  layout: {
    title: 'Cloudflare域名管理系统',
    // 启用菜单图标
    menu: {
      locale: false,
    },
    // 顶部导航布局
    layout: 'top',
    // 顶部导航主题
    navTheme: 'light',
    // 顶部导航高度
    headerHeight: 48,
    // 固定顶部
    fixedHeader: true,
    // 内容区域宽度
    contentWidth: 'Fluid',
  },
  // 忽略 moment 的语言包，减少打包体积
  ignoreMomentLocale: true,
  // 启用快速刷新
  fastRefresh: true,
  routes: [
    {
      path: '/',
      component: './dashboard',
    },
    {
      name: '登录',
      path: '/login',
      layout: false,
      component: './login',
    },
  ],
  npmClient: 'npm',
  // 禁用 mfsu 以解决依赖冲突问题
  mfsu: false,
  proxy: {
    '/api': {
      target: 'http://localhost:8080',
      changeOrigin: true,
      // 不缓存响应
      headers: {
        'Connection': 'keep-alive', // 确保代理和后端之间也试图保持长连接
        'Cache-Control': 'no-cache, no-store, must-revalidate', // 更强的无缓存指令
        'Pragma': 'no-cache', // 兼容旧版 HTTP/1.0
        'Expires': '0', // 立即过期
      },
      // 不去除 /api 前缀，因为后端的 context-path 也是 /api
      // pathRewrite: {'^/api': ''},
      onProxyRes: (proxyRes: any, req: any, res: any) => {
        if (req.headers.accept === 'text/event-stream') {
          res.writeHead(res.statusCode, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-transform',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no',
            'Access-Control-Allow-Origin': '*'
          });
        }
      }
    },
  },
});
