{"name": "cloudflare-manager-frontend", "version": "1.0.0", "private": true, "description": "Cloudflare域名管理系统前端", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "dev": "max dev", "format": "prettier --cache --write .", "postinstall": "max setup", "setup": "max setup", "start": "npm run dev", "test": "node test-frontend.js", "test:auto": "node auto-test.js", "test:simple": "node simple-test.js", "fix": "node auto-fix.js"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@ant-design/pro-components": "^2.8.7", "@ant-design/use-emotion-css": "^1.0.4", "@types/uuid": "^10.0.0", "@umijs/max": "^4.4.10", "ahooks": "^3.8.4", "antd": "^5.25.1", "classnames": "^2.3.2", "d3-color": "2.0.0", "dayjs": "^1.11.10", "lodash": "^4.17.21", "rc-field-form": "^2.7.0", "react-flow-renderer": "^10.3.17", "react-highlight-words": "^0.20.0", "react-intl": "^6.5.5", "uuid": "^11.1.0"}, "devDependencies": {"@types/lodash": "^4.17.16", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/react-highlight-words": "^0.16.7", "cross-env": "^7.0.3", "lint-staged": "^15.2.0", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^3.2.4", "prettier-plugin-packagejson": "^2.4.7", "puppeteer": "^24.8.2", "typescript": "^5.3.3"}, "resolutions": {"d3-color": "2.0.0"}}